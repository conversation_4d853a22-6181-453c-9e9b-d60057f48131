import { AnimatePresence, motion } from "framer-motion";
import { <PERSON>Check } from "lucide-react";
import React, { useEffect } from "react";
import { useLanguage } from "../context/LanguageContext";

/**
 * LoadingTransition Component
 *
 * This component displays a smooth loading transition when switching between languages
 * or after CTA interactions. It uses Framer Motion for animations and is controlled
 * by the isLoading state from LanguageContext.
 *
 * Features:
 * - Smooth fade in/out animations
 * - Prevents scrolling during loading
 * - Displays language-specific loading text
 * - Shows a polished animation without rotation
 * - Provides visual feedback about the current operation
 *
 * @returns {JSX.Element} The loading transition overlay
 */
export const LoadingTransition: React.FC = () => {
  const { isLoading, language } = useLanguage();

  // Prevent scrolling while loading
  useEffect(() => {
    if (isLoading) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [isLoading]);

  // Get loading text based on current language
  const getLoadingText = () => {
    return language === "ar" ? "جاري التحميل..." : "باركردنەوە...";
  };

  return (
    <AnimatePresence mode="wait">
      {isLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.4, ease: "easeInOut" }}
          className="fixed inset-0 bg-white/95 backdrop-blur-md z-50 flex items-center justify-center"
        >
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            transition={{
              duration: 0.3,
              ease: [0.22, 1, 0.36, 1], // Custom easing curve for smoother motion
            }}
            className="flex flex-col items-center"
          >
            {/* Enhanced loading indicator using ShieldCheck without rotation */}
            <div className="w-16 h-16 relative mb-6 flex items-center justify-center">
              {/* Pulsing circle background */}
              <motion.div
                animate={{
                  scale: [1, 1.15, 1],
                  opacity: [0.3, 0.5, 0.3],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
                className="absolute inset-0 rounded-full bg-primary/20"
              />

              {/* Secondary pulse */}
              <motion.div
                animate={{
                  scale: [1, 1.3, 1],
                  opacity: [0.1, 0.3, 0.1],
                }}
                transition={{
                  duration: 2.5,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 0.2,
                }}
                className="absolute inset-0 rounded-full bg-primary/10"
              />

              {/* Shield icon with subtle animation */}
              <motion.div
                animate={{
                  scale: [0.9, 1, 0.9],
                  opacity: [0.9, 1, 0.9],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
                className="relative z-10"
              >
                <ShieldCheck className="w-10 h-10 text-primary" />
              </motion.div>
            </div>

            {/* Loading text with subtle animation */}
            <motion.div
              animate={{
                opacity: [0.7, 1, 0.7],
                y: [0, -2, 0],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut",
              }}
              className="text-primary font-medium text-lg"
              dir={language === "ar" ? "rtl" : "ltr"}
            >
              {getLoadingText()}
            </motion.div>

            {/* Progress dots animation */}
            <motion.div
              className="flex mt-2 space-x-1 rtl:space-x-reverse"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              {[0, 1, 2].map((i) => (
                <motion.div
                  key={i}
                  className="w-2 h-2 rounded-full bg-primary/60"
                  animate={{
                    scale: [0.8, 1.2, 0.8],
                    opacity: [0.5, 1, 0.5],
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    delay: i * 0.2,
                    ease: "easeInOut",
                  }}
                />
              ))}
            </motion.div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default LoadingTransition;
