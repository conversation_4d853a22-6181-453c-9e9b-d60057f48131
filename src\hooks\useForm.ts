import { useState, useCallback, ChangeEvent } from 'react';

interface ValidationRules {
  required?: {
    value: boolean;
    message: string;
  };
  pattern?: {
    value: RegExp;
    message: string;
  };
  custom?: {
    isValid: (value: string) => boolean;
    message: string;
  };
}

type Errors<T> = Partial<Record<keyof T, string>>;

type FormState<T> = {
  values: T;
  errors: Errors<T>;
  touched: Partial<Record<keyof T, boolean>>;
  isSubmitting: boolean;
};

/**
 * useForm Hook
 * 
 * A custom hook for form handling with validation.
 * 
 * @param {T} initialValues - The initial form values
 * @param {Record<keyof T, ValidationRules>} validationRules - The validation rules
 * @param {(values: T) => void} onSubmit - The submit handler
 * @returns {Object} Form state and handlers
 */
export function useForm<T extends Record<string, any>>(
  initialValues: T,
  validationRules: Partial<Record<keyof T, ValidationRules>>,
  onSubmit: (values: T) => void
) {
  const [formState, setFormState] = useState<FormState<T>>({
    values: initialValues,
    errors: {},
    touched: {},
    isSubmitting: false,
  });

  // Validate a single field
  const validateField = useCallback(
    (name: keyof T, value: string): string => {
      const rules = validationRules[name];
      if (!rules) return '';

      // Required rule
      if (rules.required?.value && !value) {
        return rules.required.message;
      }

      // Pattern rule
      if (rules.pattern?.value && !rules.pattern.value.test(value)) {
        return rules.pattern.message;
      }

      // Custom validation rule
      if (rules.custom?.isValid && !rules.custom.isValid(value)) {
        return rules.custom.message;
      }

      return '';
    },
    [validationRules]
  );

  // Validate all fields
  const validateForm = useCallback((): Errors<T> => {
    const errors: Errors<T> = {};

    Object.keys(validationRules).forEach((key) => {
      const fieldKey = key as keyof T;
      const error = validateField(fieldKey, formState.values[fieldKey] as string);
      if (error) {
        errors[fieldKey] = error;
      }
    });

    return errors;
  }, [formState.values, validateField, validationRules]);

  // Handle input change
  const handleChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      const fieldName = name as keyof T;

      setFormState((prev) => ({
        ...prev,
        values: {
          ...prev.values,
          [fieldName]: value,
        },
        touched: {
          ...prev.touched,
          [fieldName]: true,
        },
        errors: {
          ...prev.errors,
          [fieldName]: validateField(fieldName, value),
        },
      }));
    },
    [validateField]
  );

  // Handle form submission
  const handleSubmit = useCallback(
    (e?: React.FormEvent) => {
      if (e) e.preventDefault();

      const errors = validateForm();
      const hasErrors = Object.keys(errors).length > 0;

      setFormState((prev) => ({
        ...prev,
        errors,
        isSubmitting: !hasErrors,
        touched: Object.keys(prev.values).reduce(
          (acc, key) => {
            acc[key as keyof T] = true;
            return acc;
          },
          {} as Partial<Record<keyof T, boolean>>
        ),
      }));

      if (!hasErrors) {
        onSubmit(formState.values);
        setFormState((prev) => ({
          ...prev,
          isSubmitting: false,
        }));
      }
    },
    [formState.values, onSubmit, validateForm]
  );

  // Reset form to initial values
  const resetForm = useCallback(() => {
    setFormState({
      values: initialValues,
      errors: {},
      touched: {},
      isSubmitting: false,
    });
  }, [initialValues]);

  return {
    values: formState.values,
    errors: formState.errors,
    touched: formState.touched,
    isSubmitting: formState.isSubmitting,
    handleChange,
    handleSubmit,
    resetForm,
  };
}

export default useForm; 