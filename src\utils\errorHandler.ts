import { AxiosError } from 'axios';

/**
 * <PERSON><PERSON><PERSON>
 * 
 * Utility functions for handling and formatting errors.
 */
export const errorHandler = {
  /**
   * Get error message from API error
   * 
   * @param {unknown} error - The error object
   * @returns {string} The error message
   */
  getErrorMessage(error: unknown): string {
    if (error instanceof AxiosError) {
      // API error
      if (error.response?.data?.message) {
        return error.response.data.message;
      }

      // Network error
      if (error.message === 'Network Error') {
        return 'خطأ في الاتصال بالشبكة. يرجى التحقق من اتصالك بالإنترنت.';
      }

      // Timeout error
      if (error.code === 'ECONNABORTED') {
        return 'انتهت مهلة الطلب. يرجى المحاولة مرة أخرى.';
      }

      // Other axios errors
      return error.message;
    }

    // Regular Error object
    if (error instanceof Error) {
      return error.message;
    }

    // Unknown error
    return 'حدث خطأ غير معروف. يرجى المحاولة مرة أخرى.';
  },

  /**
   * Format validation errors
   * 
   * @param {Record<string, string[]>} errors - The validation errors
   * @returns {Record<string, string>} The formatted errors
   */
  formatValidationErrors(errors: Record<string, string[]>): Record<string, string> {
    const formattedErrors: Record<string, string> = {};

    Object.keys(errors).forEach((key) => {
      formattedErrors[key] = errors[key][0];
    });

    return formattedErrors;
  },

  /**
   * Log error to console or monitoring service
   * 
   * @param {unknown} error - The error object
   * @param {string} context - The error context
   */
  logError(error: unknown, context: string): void {
    console.error(`Error in ${context}:`, error);

    // Add additional logging or error monitoring here
    // e.g., Sentry, LogRocket, etc.
  },
};

export default errorHandler; 