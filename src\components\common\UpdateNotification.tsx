import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Bell, X } from 'lucide-react';
import { useLanguage } from '../../context/LanguageContext';

interface UpdateNotificationProps {
  message?: string;
  duration?: number;
  visibleOnPageLoad?: boolean;
}

/**
 * Update Notification Component
 * 
 * Displays a temporary notification when the page is updated
 * 
 * @param {UpdateNotificationProps} props - Component props
 * @returns {JSX.Element | null} - The notification component or null if not visible
 */
const UpdateNotification: React.FC<UpdateNotificationProps> = ({
  message,
  duration = 5000, // Default 5 seconds
  visibleOnPageLoad = true,
}) => {
  const [isVisible, setIsVisible] = useState(visibleOnPageLoad);
  const { language } = useLanguage();

  // Default message by language
  const defaultMessage = language === 'ar' 
    ? 'تم تحديث هذه الصفحة' 
    : 'ئەم پەڕەیە نوێ کراوەتەوە';

  // Close the notification
  const handleClose = () => {
    setIsVisible(false);
  };

  // Auto-hide after duration
  useEffect(() => {
    if (isVisible && duration > 0) {
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, duration);
      
      return () => clearTimeout(timer);
    }
  }, [isVisible, duration]);

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -50 }}
          transition={{ duration: 0.3 }}
          className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 max-w-md w-[calc(100%-2rem)]"
        >
          <div className="bg-white shadow-lg rounded-lg p-3 px-4 flex items-center justify-between border-l-4 border-primary">
            <div className="flex items-center gap-2 text-gray-800">
              <Bell className="w-5 h-5 text-primary" />
              <span>{message || defaultMessage}</span>
            </div>
            <button 
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 transition-colors p-1"
              aria-label={language === 'ar' ? 'إغلاق الإشعار' : 'داخستنی ئاگادارکردنەوە'}
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default UpdateNotification; 