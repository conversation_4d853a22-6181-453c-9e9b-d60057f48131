import { motion } from "framer-motion";
import { CheckCircle2, Shield } from "lucide-react";
import React, { useEffect } from "react";
import { useApplicationStore } from "../store/applicationStore";
import { EventCategory, trackEvent } from "../utils/analytics";

/**
 * Renders a success page with animations and confetti effect.
 * @example
 * renderSuccessPage() 
 * <motion.div>...</motion.div>
 * @param {Object} param0 - Destructured props object (from hooks or state).
 * @returns {ReactElement} A motion div element with success messages and visuals.
 * @description
 *   - Utilizes framer-motion for animated transitions and effects.
 *   - Triggers a confetti effect upon mounting using the `useEffect` hook.
 *   - Adjusts application state to "completed" after page view event is tracked.
 *   - Contains visual cues for successful verification and information security.
 */
const Success: React.FC = () => {
  const { setStatus } = useApplicationStore();

  useEffect(() => {
    // Track page view
    trackEvent(EventCategory.PAGE, "view", "success_page");

    // Update status to completed
    setStatus("completed");
  }, [setStatus]);

  const containerVariants = {
    initial: { opacity: 0, scale: 0.9 },
    animate: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.5, ease: [0.19, 1.0, 0.22, 1.0] },
    },
    exit: {
      opacity: 0,
      scale: 0.9,
      transition: { duration: 0.3 },
    },
  };

  const itemVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
  };

  // Add confetti effect
  useEffect(() => {
    /**
     * Creates a confetti effect by animating colored div elements on the page.
     * @example
     * createConfetti()
     * // This will display animated confetti on the screen
     * @param {none} None - This function does not take any arguments.
     * @returns {void} This function does not return any value.
     * @description
     *   - Generates 100 confetti pieces with random sizes, colors, and initial positions.
     *   - Animates each confetti piece from the top of the viewport to the bottom with random x-axis movement.
     *   - Confetti pieces are automatically removed from the DOM after their animation completes.
     */
    const createConfetti = () => {
      const colors = ["#1e40af", "#10b981", "#3b82f6", "#f59e0b"];

      for (let i = 0; i < 100; i++) {
        const confetti = document.createElement("div");
        confetti.style.cssText = `
          position: fixed;
          width: ${Math.random() * 10 + 5}px;
          height: ${Math.random() * 10 + 5}px;
          background: ${colors[Math.floor(Math.random() * colors.length)]};
          top: -10px;
          left: ${Math.random() * 100}vw;
          opacity: ${Math.random() + 0.5};
          pointer-events: none;
          z-index: 1000;
          border-radius: 50%;
          transform: rotate(${Math.random() * 360}deg);
        `;

        document.body.appendChild(confetti);

        const animationDuration = Math.random() * 3 + 2;
        const xMovement = (Math.random() - 0.5) * 40;

        confetti.animate(
          [
            { transform: `translate(0, 0) rotate(0deg)` },
            {
              transform: `translate(${xMovement}vw, 100vh) rotate(${Math.random() * 360}deg)`,
            },
          ],
          {
            duration: animationDuration * 1000,
            iterations: 1,
            easing: "cubic-bezier(0.215, 0.61, 0.355, 1)",
            fill: "forwards",
          }
        );

        setTimeout(() => {
          document.body.removeChild(confetti);
        }, animationDuration * 1000);
      }
    };

    createConfetti();
  }, []);

  return (
    <motion.div
      variants={containerVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className="max-w-md mx-auto py-6 px-4 sm:px-6 md:py-10"
    >
      <div className="bg-white rounded-2xl shadow-xl p-6 sm:p-8 relative overflow-hidden">
        {/* Animated success icon */}
        <div className="flex justify-center mb-6">
          <motion.div
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{
              type: "spring",
              stiffness: 260,
              damping: 20,
              delay: 0.2,
            }}
            className="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center"
          >
            <CheckCircle2 className="w-14 h-14 text-green-600" />
          </motion.div>
        </div>

        {/* Success message */}
        <motion.div
          variants={itemVariants}
          transition={{ delay: 0.4 }}
          className="text-center mb-8"
        >
          <h1 className="text-2xl font-bold text-gray-800 mb-3">
            تمت العملية بنجاح
          </h1>
          <p className="text-gray-600">
            تم التحقق من بياناتك بنجاح وقد تم إكمال العملية
          </p>
        </motion.div>

        {/* Additional information */}
        <motion.div
          variants={itemVariants}
          transition={{ delay: 0.6 }}
          className="space-y-4 mb-8"
        >
          <div className="flex items-center bg-blue-50 p-4 rounded-lg">
            <Shield className="w-5 h-5 text-blue-600 ml-3 flex-shrink-0" />
            <div>
              <p className="text-blue-700 font-medium text-sm">
                تم تأمين البيانات المالية الخاصة بك
              </p>
            </div>
          </div>

          <div className="flex items-center bg-green-50 p-4 rounded-lg">
            <CheckCircle2 className="w-5 h-5 text-green-600 ml-3 flex-shrink-0" />
            <div>
              <p className="text-green-700 font-medium text-sm">
                تم إكمال عملية التحقق بنجاح
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default Success;
