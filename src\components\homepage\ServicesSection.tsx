import React, { useCallback, useMemo } from 'react';
import { Wallet, CreditCard, Users, Building2, Qr<PERSON>ode, PiggyBank } from 'lucide-react';
import { useLanguage } from '../../context/LanguageContext';
import ServiceCard from '../ServiceCard';

interface ServicesSectionProps {
  onServiceClick: (index: number, title: string) => void;
  selectedService: number | null;
}

/**
 * ServicesSection Component
 * 
 * Displays a grid of service cards with optimized rendering for performance.
 * 
 * @param {ServicesSectionProps} props - The component props
 * @returns {JSX.Element} The services section component
 */
const ServicesSection: React.FC<ServicesSectionProps> = React.memo(({ 
  onServiceClick, 
  selectedService 
}) => {
  const { language } = useLanguage();

  // Services data - memoized to prevent recreating on each render
  const services = useMemo(() => [
    {
      icon: <Wallet className="w-7 h-7 text-primary" />,
      title: language === 'ar' ? 'الحساب الشخصي' : 'حیسابی کەسی',
      subtitle: language === 'ar' ? 'Personal Account' : 'الحساب الشخصي',
      description: language === 'ar' ? 'غير حياتك مع حساب شخصي من البنك العراقي الأول' : 'ژیانت بگۆڕە لەگەڵ حیسابێکی کەسی لە بانکی یەکەمی عێراق'
    },
    {
      icon: <Building2 className="w-7 h-7 text-primary" />,
      title: language === 'ar' ? 'حساب الأعمال' : 'حیسابی بازرگانی',
      subtitle: language === 'ar' ? 'Business Account' : 'حساب الأعمال',
      description: language === 'ar' ? 'تعزيز نمو الأعمال في العراق' : 'پشتگیری گەشەی بازرگانی لە عێراق'
    },
    {
      icon: <CreditCard className="w-7 h-7 text-primary" />,
      title: language === 'ar' ? 'خدمات البطاقات' : 'خزمەتگوزاری کارت',
      subtitle: language === 'ar' ? 'Card Services' : 'خدمات البطاقات',
      description: language === 'ar' ? 'بطاقات الائتمان والخصم لاحتياجاتك' : 'کارتی کرێدیت و دیبیت بۆ پێداویستیەکانت'
    },
    {
      icon: <QrCode className="w-7 h-7 text-primary" />,
      title: language === 'ar' ? 'مدفوعات QR' : 'پارەدانی QR',
      subtitle: language === 'ar' ? 'QR Payments' : 'مدفوعات QR',
      description: language === 'ar' ? 'ادفع باستخدام رمز QR' : 'پارە بدە بە بەکارهێنانی کۆدی QR'
    },
    {
      icon: <Users className="w-7 h-7 text-primary" />,
      title: language === 'ar' ? 'بطاقة العائلة' : 'کارتی خێزانی',
      subtitle: language === 'ar' ? 'Family Card' : 'بطاقة العائلة',
      description: language === 'ar' ? 'إدارة مالية الأسرة' : 'بەڕێوەبردنی دارایی خێزان'
    },
    {
      icon: <PiggyBank className="w-7 h-7 text-primary" />,
      title: language === 'ar' ? 'حساب الاستثمار' : 'حیسابی وەبەرهێنان',
      subtitle: language === 'ar' ? 'Investment Account' : 'حساب الاستثمار',
      description: language === 'ar' ? 'استراتيجيات استثمار مخصصة' : 'ستراتیژی وەبەرهێنانی تایبەت'
    }
  ], [language]);

  // Create memoized click handlers for each service to prevent re-renders
  const handleServiceClick = useCallback((index: number, title: string) => {
    onServiceClick(index, title);
  }, [onServiceClick]);

  return (
    <section className="mt-16 mb-16 px-4 sm:px-6 max-w-7xl mx-auto">
      <div className="text-center mb-10">
        <h2 className="text-2xl md:text-3xl font-bold text-primary mb-4">
          {language === 'ar' ? 'خدماتنا المصرفية' : 'خزمەتگوزاریە بانکیەکانمان'}
        </h2>
        <p className="text-gray max-w-2xl mx-auto">
          {language === 'ar' 
            ? 'اكتشف الخدمات المصرفية التي يقدمها البنك العراقي الأول' 
            : 'خزمەتگوزاریە بانکیەکانی بانکی یەکەمی عێراق تاقی بکەوە'}
        </p>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto service-cards-container">
        {services.map((service, index) => (
          <div
            key={index}
            className="service-card-wrapper transform hover:-translate-y-1 transition-transform duration-200"
          >
            <ServiceCard
              {...service}
              onClick={() => handleServiceClick(index, service.title)}
              className={`
                ${selectedService === index ? 'ring-2 ring-primary' : ''}
                w-full h-full transition-all duration-300 hover:shadow-lg
              `}
            />
          </div>
        ))}
      </div>
    </section>
  );
}, (prevProps, nextProps) => {
  // Custom comparison for React.memo
  // Only re-render if selectedService changes
  return prevProps.selectedService === nextProps.selectedService;
});

export default ServicesSection; 