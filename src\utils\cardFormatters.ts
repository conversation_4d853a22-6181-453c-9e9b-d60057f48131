// Card formatting utilities
/**
* Formats a string of digits into groups of four separated by spaces
* @example
* formatCardNumber("1234567812345678")
* // returns "1234 5678 1234 5678"
* @param {string} value - The input string that potentially contains a sequence of digits.
* @returns {string} The formatted string with digits grouped in sets of four or the original value if no groups are formed.
* @description
*   - Strips all whitespace and non-numeric characters from the input string.
*   - Joins groups of four digits with a single space between them.
*   - Maintains the original input if fewer than four consecutive digits are present.
*/
export const formatCardNumber = (value: string): string => {
  const v = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "");
  const matches = v.match(/\d{4,16}/g);
  const match = (matches && matches[0]) || "";
  const parts = [];

  for (let i = 0, len = match.length; i < len; i += 4) {
    parts.push(match.substring(i, i + 4));
  }

  if (parts.length) {
    return parts.join(" ");
  } else {
    return value;
  }
};

/**
 * Formats a string to represent a card expiration date in a "MM/YY" format.
 * @example
 * formatExpiryDate("1234")
 * // returns "12/34"
 * @param {string} value - A string possibly containing a card expiration date.
 * @returns {string} A formatted expiration date string in the form "MM/YY".
 * @description
 *   - Removes all spaces and non-numeric characters from the input.
 *   - Adjusts the month to "12" if the parsed month exceeds "12".
 *   - Returns the clean numeric input directly if it's shorter than two digits.
 */
export const formatExpiry = (value: string): string => {
  const cleanValue = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "");

  if (cleanValue.length >= 2) {
    const month = cleanValue.substring(0, 2);
    const year = cleanValue.substring(2, 4);

    if (parseInt(month) > 12) {
      return "12/" + year;
    }

    if (cleanValue.length >= 4) {
      return month + "/" + year;
    }

    if (cleanValue.length >= 2) {
      return (
        month + (cleanValue.length > 2 ? "/" + cleanValue.substring(2) : "")
      );
    }
  }

  return cleanValue;
};

export const formatCVC = (value: string): string => {
  return value.replace(/[^0-9]/g, "").substring(0, 3);
};

/**
 * Determines the credit card type based on the provided card number.
 * @example
 * getCardType("****************")
 * "visa"
 * @param {string} number - The card number which might include non-digit characters.
 * @returns {string} The type of the card (e.g., 'visa', 'mastercard', 'amex', etc.) or 'unknown'.
 * @description
 *   - Cleans the card number by removing all non-digit characters before processing.
 *   - Utilizes regular expressions to match card number patterns for different card issuers.
 *   - Iterates over a predefined set of card patterns to determine the card type.
 *   - If the card number does not match any pattern, returns 'unknown'.
 */
export const getCardType = (number: string): string => {
  const cleanNumber = number.replace(/[^\d]/g, "");

  const cardPatterns = {
    visa: /^4/,
    mastercard: /^(5[1-5]|2[2-7])/,
    amex: /^3[47]/,
    discover: /^6(?:011|5)/,
    unionpay: /^62/,
    troy: /^9792/,
    diners: /^3(?:0[0-5]|[68])/,
  };

  for (const [type, pattern] of Object.entries(cardPatterns)) {
    if (pattern.test(cleanNumber)) {
      return type;
    }
  }

  return "unknown";
};

/**
 * Formats a card number string with spaces every 4 digits
 * @param value The raw card number input
 * @returns Formatted card number with spaces
 */
export const normalizeCardNumber = (value: string): string => {
  // Remove all non-digit characters
  const numbers = value.replace(/\D/g, "");

  // Only allow 16 digits max
  const trimmed = numbers.substring(0, 16);

  // Format with spaces after every 4 digits
  const groups = trimmed.match(/.{1,4}/g) || [];
  return groups.join(" ");
};

/**
 * Formats an expiry date with format MM/YY
 * @param value The raw expiry date input
 * @returns Formatted expiry date
 */
export const formatExpiryDate = (value: string): string => {
  // Remove all non-digit characters
  const numbers = value.replace(/\D/g, "");

  if (numbers.length === 0) {
    return "";
  }

  // Handle first digit constraints (month can only start with 0, 1)
  if (numbers.length === 1) {
    if (numbers[0] !== "0" && numbers[0] !== "1") {
      return "0" + numbers[0];
    }
    return numbers;
  }

  // Handle second digit constraints (month can only be 01-12)
  if (numbers.length >= 2) {
    const month = parseInt(numbers.substring(0, 2));
    if (month > 12) {
      return "0" + numbers[0] + "/" + numbers.substring(1, 3);
    }

    // Format with slash after month
    if (numbers.length > 2) {
      return numbers.substring(0, 2) + "/" + numbers.substring(2, 4);
    }

    return numbers.substring(0, 2);
  }

  return numbers;
};
