import { shouldPolyfill } from "@formatjs/intl-numberformat/should-polyfill";

/**
 * Load Intl polyfills for Kurdish (ku) locale
 * This fixes the issue: "Missing locale data for locale: "ku" in Intl.NumberFormat"
 */
export async function loadIntlPolyfills() {
  // Check if NumberFormat needs to be polyfilled for Kurdish
  if (shouldPolyfill()) {
    // Load the polyfill
    await import("@formatjs/intl-numberformat/polyfill");

    // Load Arabic as the fallback locale data (since Kurdish uses Arabic numerals)
    await import("@formatjs/intl-numberformat/locale-data/ar");

    // We also need to explicitly register the Kurdish locale
    // using Arabic number formatting as a fallback
    if (!Intl.NumberFormat.supportedLocalesOf(["ku"]).length) {
      // If Kurdish is not supported, we use a custom definition
      // that inherits from Arabic (similar numerical system)
      const localeData = {
        data: {
          ku: {
            locale: "ku",
            parentLocales: ["ar"], // Inherit from Arabic
          },
        },
        availableLocales: ["ku"],
      };

      // @ts-ignore - Type definitions might not include this method
      Intl.NumberFormat.__addLocaleData(localeData);

      console.log("Kurdish locale polyfill loaded for Intl.NumberFormat");
    }
  }
}
