import api from './api';

// Types
export interface LoginCredentials {
  phone: string;
  password: string;
}

export interface VerificationData {
  phone: string;
  code: string;
}

export interface UserData {
  id: string;
  name: string;
  phone: string;
  // Add other user fields as needed
}

/**
 * Authentication Service
 * 
 * Handles user authentication, verification, and user data.
 */
export const authService = {
  /**
   * Login with phone and password
   * 
   * @param {LoginCredentials} credentials - The login credentials
   * @returns {Promise<UserData>} The user data
   */
  async login(credentials: LoginCredentials): Promise<UserData> {
    const response = await api.post<{ user: UserData; token: string }>('/auth/login', credentials);

    // Save token to localStorage
    localStorage.setItem('auth_token', response.data.token);

    return response.data.user;
  },

  /**
   * Request verification code
   * 
   * @param {string} phone - The phone number
   * @returns {Promise<{ success: boolean }>} The response
   */
  async requestVerification(phone: string): Promise<{ success: boolean }> {
    const response = await api.post<{ success: boolean }>('/auth/request-verification', { phone });
    return response.data;
  },

  /**
   * Verify code
   * 
   * @param {VerificationData} data - The verification data
   * @returns {Promise<{ user: UserData; token: string }>} The response
   */
  async verifyCode(data: VerificationData): Promise<{ user: UserData; token: string }> {
    const response = await api.post<{ user: UserData; token: string }>('/auth/verify', data);

    // Save token to localStorage
    localStorage.setItem('auth_token', response.data.token);

    return response.data;
  },

  /**
   * Logout user
   * 
   * @returns {Promise<void>}
   */
  async logout(): Promise<void> {
    try {
      await api.post('/auth/logout');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Always clear local storage
      localStorage.removeItem('auth_token');
    }
  },

  /**
   * Get current user data
   * 
   * @returns {Promise<UserData>} The user data
   */
  async getCurrentUser(): Promise<UserData> {
    const response = await api.get<UserData>('/auth/me');
    return response.data;
  },

  /**
   * Check if user is authenticated
   * 
   * @returns {boolean} Whether the user is authenticated
   */
  isAuthenticated(): boolean {
    return !!localStorage.getItem('auth_token');
  },
};

export default authService; 