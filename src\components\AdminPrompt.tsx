import React, { useState } from 'react';
import { processNaturalLanguageRequest, AdminResponse } from '../services/adminApi';

/**
 * AdminPrompt Component
 * 
 * A simple component that allows admins to interact with the system using natural language.
 * This is a minimal implementation to demonstrate the AI-powered admin API.
 * 
 * @returns {JSX.Element} The admin prompt component
 */
export const AdminPrompt: React.FC = () => {
  const [prompt, setPrompt] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [response, setResponse] = useState<AdminResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  /**
   * Handle prompt submission
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!prompt.trim()) {
      setError('Please enter a prompt');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await processNaturalLanguageRequest(prompt);
      setResponse(result);

      if (!result.success) {
        setError(result.error || 'Unknown error occurred');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      setResponse(null);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Format response data for display
   */
  const formatResponseData = (data: any): string => {
    if (!data) return 'No data';

    try {
      return JSON.stringify(data, null, 2);
    } catch (err) {
      return String(data);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold text-primary mb-6">Admin AI Assistant</h2>

      <form onSubmit={handleSubmit} className="mb-6">
        <div className="mb-4">
          <label htmlFor="prompt" className="block text-gray-700 mb-2">
            Enter your request in natural language
          </label>
          <input
            type="text"
            id="prompt"
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder="e.g., Show me statistics for personal loans"
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
            disabled={isLoading}
          />
        </div>

        <button
          type="submit"
          disabled={isLoading}
          className="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Processing...' : 'Submit'}
        </button>
      </form>

      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700">
          <p className="font-medium">Error</p>
          <p>{error}</p>
        </div>
      )}

      {response && (
        <div className="response-container">
          <h3 className="text-xl font-semibold mb-2">Response</h3>

          {response.message && (
            <div className="mb-4 p-4 bg-gray-50 rounded-lg">
              <p className="text-gray-800">{response.message}</p>
            </div>
          )}

          {response.data && (
            <div className="mb-4">
              <h4 className="font-medium mb-2">Data</h4>
              <pre className="bg-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
                {formatResponseData(response.data)}
              </pre>
            </div>
          )}

          {response.requestId && (
            <div className="text-xs text-gray-500">
              Request ID: {response.requestId}
            </div>
          )}
        </div>
      )}

      <div className="mt-6 border-t pt-4">
        <h3 className="text-lg font-medium mb-2">Example prompts:</h3>
        <ul className="list-disc list-inside space-y-1 text-gray-700">
          <li>Show me statistics for all offers</li>
          <li>Get performance data for personal loans</li>
          <li>Update the title of car loan offer to "Premium Auto Financing"</li>
          <li>Show me all user segments</li>
          <li>Create a new offer with title "Student Loan" and description "Special financing for students"</li>
        </ul>
      </div>
    </div>
  );
}; 