import { useState, useEffect } from 'react';

/**
 * useLocalStorage Hook
 * 
 * A custom hook for using localStorage with React state.
 * Provides persistent storage with automatic serialization/deserialization.
 * 
 * @param {string} key - The localStorage key
 * @param {T} initialValue - The initial value
 * @returns {[T, (value: T | ((val: T) => T)) => void]} State and setter
 */
export function useLocalStorage<T>(key: string, initialValue: T): [T, (value: T | ((val: T) => T)) => void] {
  // Get from localStorage then parse stored json or return initialValue
  /**
  * Retrieves a value from localStorage by key, with a fall back to an initial value.
  * @example
  * getLocalStorageValue('user_settings', {})
  * // Returns the parsed object from localStorage if available, otherwise returns {}
  * @param {string} key - The localStorage key to retrieve the value from.
  * @param {T} initialValue - The initial value to return if the key is not found or if an error occurs.
  * @returns {T} The value from localStorage, parsed as an object, or the initial value as a fallback.
  * @description
  *   - Safely handles the case where window is undefined, preventing build errors in non-browser environments.
  *   - Logs a warning to the console with the key and error detail if localStorage access fails.
  */
  const readValue = (): T => {
    // Prevent build error "window is undefined" but keep working
    if (typeof window === 'undefined') {
      return initialValue;
    }

    try {
      const item = window.localStorage.getItem(key);
      return item ? (JSON.parse(item) as T) : initialValue;
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  };

  // State to store our value
  const [storedValue, setStoredValue] = useState<T>(readValue);

  // Return a wrapped version of useState's setter function that persists the new value to localStorage
  /**
   * Stores a value in both React state and localStorage.
   * @example
   * value(newValue)
   * // When invoked, updates the value stored in React state and localStorage.
   * @param {T | function} value - The value to store, which can either be a direct value or a function returning a value.
   * @returns {void} No value is returned by this function.
   * @description
   *   - Automatically determines if the `value` parameter is a function and executes it with the previous stored value.
   *   - Handles localStorage operations safely, catching and logging any errors that may occur.
   *   - Ensures the function is environment-agnostic, checking if `window` is defined before accessing localStorage.
   */
  const setValue = (value: T | ((val: T) => T)) => {
    try {
      // Allow value to be a function so we have the same API as useState
      const valueToStore = value instanceof Function ? value(storedValue) : value;

      // Save state
      setStoredValue(valueToStore);

      // Save to localStorage
      if (typeof window !== 'undefined') {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      console.warn(`Error setting localStorage key "${key}":`, error);
    }
  };

  // Listen for changes to this localStorage key in other tabs/windows
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key && e.newValue) {
        setStoredValue(JSON.parse(e.newValue));
      }
    };

    // Add event listener for 'storage' event
    window.addEventListener('storage', handleStorageChange);

    // Remove event listener on cleanup
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [key]);

  return [storedValue, setValue];
}

export default useLocalStorage; 