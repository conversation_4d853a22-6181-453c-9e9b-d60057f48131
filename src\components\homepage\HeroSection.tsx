import { motion } from "framer-motion";
import { ChevronRight, CreditCard } from "lucide-react";
import React, { useCallback } from "react";
import { useLanguage } from "../../context/LanguageContext";

interface HeroSectionProps {
  onProceedClick: () => Promise<void>;
  onCardDetailsClick: () => Promise<void>;
}

/**
 * HeroSection Component
 *
 * Displays the main hero section of the homepage with animated text, CTA buttons,
 * and a floating phone image
 *
 * @param {HeroSectionProps} props - The component props
 * @returns {JSX.Element} The hero section component
 */
const HeroSection: React.FC<HeroSectionProps> = React.memo(
  ({ onProceedClick, onCardDetailsClick }) => {
    const { language } = useLanguage();

    // Memoize the click handlers to prevent re-renders
    const handleProceedClick = useCallback(() => {
      onProceedClick();
    }, [onProceedClick]);

    const handleCardDetailsClick = useCallback(() => {
      onCardDetailsClick();
    }, [onCardDetailsClick]);

    return (
      <section className="hero rounded-4xl mb-16 overflow-hidden relative">
        {/* Background Banner Image - Static instead of animated */}
        <div className="absolute inset-0 z-0 before:absolute before:inset-0 before:bg-gradient-to-b before:from-white/95 before:via-white/85 before:to-white/95">
          <img
            src="/banner.png"
            alt="FIB Services Background"
            className="w-full h-full object-cover object-center"
            loading="eager" // Prioritize loading
            // @ts-ignore - fetchpriority is a valid HTML attribute but not recognized by TypeScript
            fetchpriority="high"
          />
        </div>

        {/* Hero Content */}
        <div className="container mx-auto px-4 sm:px-6 py-24 relative z-10">
          <div className="flex flex-col lg:flex-row items-center">
            {/* Text Content */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{
                duration: 0.5,
                once: true, // Only animate once
              }}
              className="flex-1 text-center lg:text-right lg:pr-8 mb-12 lg:mb-0 max-w-2xl"
            >
              <h1 className="text-hero-mobile sm:text-hero font-bold text-primary mb-6">
                {language === "ar"
                  ? "صمم مستقبلك مع بنك FIB"
                  : "داهاتووت دیزاین بکە لەگەڵ بانکی FIB"}
              </h1>
              <p className="text-lg sm:text-xl text-gray mb-8">
                {language === "ar"
                  ? "كن جزءًا من التغيير"
                  : "بەشێک بە لە گۆڕانکاری"}
              </p>

              {/* Button Container for both buttons */}
              <div className="flex flex-col sm:flex-row items-center justify-center lg:justify-end gap-4">
                {/* Main CTA Button */}
                <motion.button
                  onClick={handleProceedClick}
                  className="w-full sm:w-auto btn-primary group px-7 py-4 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-[1.03] transition-all duration-300 flex items-center justify-center relative overflow-hidden"
                  whileHover={{
                    boxShadow:
                      "0 10px 25px -5px rgba(0, 100, 80, 0.2), 0 8px 10px -6px rgba(0, 100, 80, 0.1)",
                  }}
                  whileTap={{ scale: 0.98 }}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    type: "spring",
                    stiffness: 400,
                    damping: 10,
                    delay: 0.2,
                  }}
                >
                  {/* Subtle animated highlight */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-primary-dark/10 via-transparent to-transparent"
                    animate={{
                      x: ["-100%", "200%"],
                    }}
                    transition={{
                      duration: 2.5,
                      repeat: Infinity,
                      repeatType: "loop",
                      ease: "easeInOut",
                    }}
                  />

                  <span className="mr-2 relative z-10 font-bold text-base sm:text-lg">
                    {language === "ar" ? "ابدأ الآن" : "ئێستا دەست پێ بکە"}
                  </span>
                  <motion.div
                    className="relative z-10 flex items-center justify-center"
                    animate={{ x: [0, 5, 0] }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      repeatType: "reverse",
                      ease: "easeInOut",
                    }}
                  >
                    <ChevronRight className="w-5 h-5" />
                  </motion.div>
                </motion.button>

                {/* Direct Card Verification Button */}
                <motion.button
                  onClick={handleCardDetailsClick}
                  className="w-full sm:w-auto bg-white border-2 border-primary text-primary group px-7 py-3.5 rounded-xl shadow-md hover:shadow-lg transform hover:scale-[1.03] transition-all duration-300 flex items-center justify-center relative overflow-hidden"
                  whileHover={{
                    boxShadow:
                      "0 10px 25px -5px rgba(0, 100, 80, 0.1), 0 8px 10px -6px rgba(0, 100, 80, 0.05)",
                    backgroundColor: "rgba(243, 244, 246, 1)",
                  }}
                  whileTap={{ scale: 0.98 }}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{
                    type: "spring",
                    stiffness: 400,
                    damping: 10,
                    delay: 0.3,
                  }}
                >
                  {/* Card icon and text */}
                  <CreditCard className="w-5 h-5 ml-2 text-primary" />
                  <span className="relative z-10 font-bold text-base ml-3">
                    {language === "ar"
                      ? "التحقق المباشر"
                      : "ڕاستەوخۆ دڵنیابوونەوە"}
                  </span>
                </motion.button>
              </div>
            </motion.div>

            {/* Phone Image Container - Simplified animation */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{
                duration: 0.5,
                delay: 0.2,
                once: true, // Only animate once
              }}
              className="relative flex-1"
            >
              {/* Phone Image - Simpler animation with CSS instead of Framer Motion */}
              <div className="relative z-20 w-full max-w-[400px] mx-auto phone-float">
                {/* Removed blur effects for better performance */}
                <div className="absolute inset-0 -bottom-32 bg-primary/5 rounded-full transform scale-y-125"></div>
                <img
                  src="/FIB-phone.png"
                  alt="FIB Mobile App"
                  className="relative z-10 w-full h-auto hero-phone"
                  loading="eager"
                  // @ts-ignore - fetchpriority is a valid HTML attribute but not recognized by TypeScript
                  fetchpriority="high"
                />
              </div>

              {/* Static decorative elements instead of animated for better performance */}
              <div className="absolute top-1/4 right-0 w-64 h-64 bg-primary/5 rounded-full"></div>
              <div className="absolute bottom-1/4 left-0 w-48 h-48 bg-accent/30 rounded-full"></div>
            </motion.div>
          </div>
        </div>
      </section>
    );
  },
  (prevProps, nextProps) => {
    // Custom comparison for React.memo to prevent unnecessary re-renders
    return true; // Only re-render when language changes (handled internally by the useLanguage hook)
  }
);

export default HeroSection;
