import React from 'react';
import { motion } from 'framer-motion';

export interface CardProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'outlined' | 'glass';
  className?: string;
  onClick?: () => void;
  interactive?: boolean;
  fullWidth?: boolean;
}

/**
 * Card Component
 * 
 * A reusable card component with various styles.
 * Supports interactive mode with hover and tap animations.
 * 
 * @param {CardProps} props - The card props
 * @returns {JSX.Element} The card component
 */
export const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  className = '',
  onClick,
  interactive = false,
  fullWidth = false,
}) => {
  // Variant styles
  const variantStyles = {
    default: 'bg-white shadow-sm',
    elevated: 'bg-white shadow-md',
    outlined: 'bg-white border border-gray-200',
    glass: 'bg-white/70 backdrop-blur-md shadow-sm',
  };

  // Combined classes
  const cardClasses = `
    ${variantStyles[variant]}
    ${fullWidth ? 'w-full' : ''}
    ${interactive ? 'cursor-pointer' : ''}
    rounded-xl p-4
    ${className}
  `;

  return (
    <motion.div
      className={cardClasses}
      onClick={onClick}
      whileHover={interactive ? { scale: 1.02, y: -5 } : {}}
      whileTap={interactive ? { scale: 0.98 } : {}}
      transition={{ type: 'spring', stiffness: 400, damping: 17 }}
    >
      {children}
    </motion.div>
  );
};

/**
 * Card.Header Component
 * 
 * A header section for the Card component.
 */
Card.Header = function CardHeader({
  children,
  className = '',
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return <div className={`mb-4 ${className}`}>{children}</div>;
};

/**
 * Card.Body Component
 * 
 * A body section for the Card component.
 */
Card.Body = function CardBody({
  children,
  className = '',
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return <div className={className}>{children}</div>;
};

/**
 * Card.Footer Component
 * 
 * A footer section for the Card component.
 */
Card.Footer = function CardFooter({
  children,
  className = '',
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return <div className={`mt-4 ${className}`}>{children}</div>;
};

export default Card; 