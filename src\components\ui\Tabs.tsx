import React, { createContext, useContext, useState } from 'react';

// Context for the tabs
interface TabsContextType {
  value: string;
  onValueChange: (value: string) => void;
}

const TabsContext = createContext<TabsContextType | undefined>(undefined);

// Hook to use the tabs context
const useTabs = () => {
  const context = useContext(TabsContext);
  if (!context) {
    throw new Error('Tabs components must be used within a TabsProvider');
  }
  return context;
};

// Tabs component
interface TabsProps {
  value: string;
  onValueChange: (value: string) => void;
  children: React.ReactNode;
  className?: string;
}

/**
* Provides a context and structure for tabs with customizable classes.
* @example
* tabsComponent({value: currentValue, onValueChange: handleValueChange, children: tabElements, className: 'custom-tabs'})
* A JSX structure of configurable tabs with the current context value.
* @param {Object} inputs - Contains the properties used by the tabs component.
* @param {any} inputs.value - The current active tab value.
* @param {Function} inputs.onValueChange - Callback function to update the active tab value.
* @param {ReactNode} inputs.children - Tab elements to be rendered within the component.
* @param {string} [inputs.className] - Additional custom class names for the tabs container.
* @returns {JSX.Element} The JSX structure of tabs with context and styles applied.
* @description
*   - Utilizes a TabsContext to provide and consume the active tab value and update function.
*   - Allows additional CSS classes to be applied for further styling flexibility.
*   - The 'children' prop should contain the tab components to be displayed.
*/
export const Tabs: React.FC<TabsProps> = ({
  value,
  onValueChange,
  children,
  className = ''
}) => {
  return (
    <TabsContext.Provider value={{ value, onValueChange }}>
      <div className={`tabs ${className}`}>
        {children}
      </div>
    </TabsContext.Provider>
  );
};

// TabsList component
interface TabsListProps {
  children: React.ReactNode;
  className?: string;
}

export const TabsList: React.FC<TabsListProps> = ({
  children,
  className = ''
}) => {
  return (
    <div className={`tabs-list bg-gray-100 rounded-lg p-1 ${className}`}>
      {children}
    </div>
  );
};

// TabsTrigger component
interface TabsTriggerProps {
  value: string;
  children: React.ReactNode;
  className?: string;
}

/**
 * Renders a button element that acts as a tab trigger.
 * @example
 * tabsTrigger({ value: 'tab1', children: 'Tab 1', className: 'custom-class' })
 * <button type="button" role="tab" class="tabs-trigger py-2 px-4 text-sm font-medium transition-all rounded-md custom-class" aria-selected="false">Tab 1</button>
 * @param {Object} { value, children, className } - The props for the tab trigger.
 * @param {string} value - The value of the tab.
 * @param {React.ReactNode} children - The content to be rendered within the tab.
 * @param {string} [className=''] - Optional additional class names for styling.
 * @returns {JSX.Element} A JSX button element configured as a tab trigger.
 * @description
 *   - The button toggles its active state based on the `selectedValue` from the `useTabs` hook.
 *   - Applies conditional styles based on whether the tab is active or not.
 *   - Invokes `onValueChange` from `useTabs` to change the selected tab value when clicked.
 */
export const TabsTrigger: React.FC<TabsTriggerProps> = ({
  value,
  children,
  className = ''
}) => {
  const { value: selectedValue, onValueChange } = useTabs();
  const isActive = selectedValue === value;

  return (
    <button
      type="button"
      role="tab"
      aria-selected={isActive}
      onClick={() => onValueChange(value)}
      className={`tabs-trigger py-2 px-4 text-sm font-medium transition-all rounded-md ${isActive
          ? 'bg-white text-primary shadow-sm'
          : 'text-gray-600 hover:text-primary hover:bg-gray-50'
        } ${className}`}
    >
      {children}
    </button>
  );
};

// TabsContent component
interface TabsContentProps {
  value: string;
  children: React.ReactNode;
  className?: string;
}

/**
 * Renders tab content when a tab is active.
 * @example
 * TabsComponent({ value: 'tab1', children: <div>Content</div>, className: 'custom-class' })
 * Returns <div role="tabpanel" className="tabs-content custom-class">Content</div> if 'tab1' is active.
 * @param {string} value - Identifier for the tab.
 * @param {React.ReactNode} children - Content to be rendered inside the tab panel.
 * @param {string} className - Additional CSS classes for styling.
 * @returns {React.ReactNode | null} Returns the tab panel div if the tab is active, otherwise returns null.
 * @description
 *   - Utilizes useTabs hook to determine the active tab.
 *   - Appends extra class names for dynamic styling.
 */
export const TabsContent: React.FC<TabsContentProps> = ({
  value,
  children,
  className = ''
}) => {
  const { value: selectedValue } = useTabs();
  const isActive = selectedValue === value;

  if (!isActive) return null;

  return (
    <div
      role="tabpanel"
      className={`tabs-content ${className}`}
    >
      {children}
    </div>
  );
}; 