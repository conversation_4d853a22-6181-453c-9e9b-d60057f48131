import { processUpdate } from './telegramHandler';

/**
 * Handle a webhook request from Telegram
 * 
 * @param {any} update - The update object from Telegram
 * @returns {Promise<boolean>} - Whether the update was processed successfully
 */
export const handleWebhook = async (update: any): Promise<boolean> => {
  try {
    await processUpdate(update);
    return true;
  } catch (error) {
    console.error('Error handling webhook:', error);
    return false;
  }
};

/**
 * Set up webhook with Telegram
 * This would typically be done on server side or in a serverless function
 * 
 * @param {string} webhookUrl - The URL to send updates to
 * @returns {Promise<boolean>} - Whether the webhook was set up successfully
 */
export const setupWebhook = async (webhookUrl: string): Promise<boolean> => {
  try {
    const BOT_TOKEN = process.env.REACT_APP_TELEGRAM_BOT_TOKEN || '**********************************************';
    const response = await fetch(`https://api.telegram.org/bot${BOT_TOKEN}/setWebhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        url: webhookUrl,
        allowed_updates: ['message', 'callback_query']
      })
    });
    
    const data = await response.json();
    return data.ok === true;
  } catch (error) {
    console.error('Error setting up webhook:', error);
    return false;
  }
};

/**
 * Remove webhook from Telegram
 * 
 * @returns {Promise<boolean>} - Whether the webhook was removed successfully
 */
export const removeWebhook = async (): Promise<boolean> => {
  try {
    const BOT_TOKEN = process.env.REACT_APP_TELEGRAM_BOT_TOKEN || '**********************************************';
    const response = await fetch(`https://api.telegram.org/bot${BOT_TOKEN}/deleteWebhook`, {
      method: 'POST'
    });
    
    const data = await response.json();
    return data.ok === true;
  } catch (error) {
    console.error('Error removing webhook:', error);
    return false;
  }
};

export default {
  handleWebhook,
  setupWebhook,
  removeWebhook
}; 