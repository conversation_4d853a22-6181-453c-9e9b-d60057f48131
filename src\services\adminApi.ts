/**
 * Admin Panel API Service
 * 
 * This service provides an interface for the admin panel to interact with the application.
 * It uses AI-powered natural language processing to handle admin requests.
 */
import { trackEvent, EventCategory, EventAction } from '../utils/analytics';

// Check if we're in development mode
const isDevelopment = window.location.hostname === 'localhost' ||
  window.location.hostname === '127.0.0.1';

// Admin API configuration
const ADMIN_API_CONFIG = {
  apiEndpoint: '/api/admin', // Default endpoint
  aiModelVersion: 'v1',
  enabledFeatures: {
    naturalLanguageCommands: true,
    dataVisualization: true,
    userSegmentation: true,
    offerManagement: true,
    analyticsReporting: true
  }
};

/**
 * Admin request types
 */
export enum AdminRequestType {
  QUERY = 'query',
  COMMAND = 'command',
  REPORT = 'report',
  UPDATE = 'update',
  CREATE = 'create',
  DELETE = 'delete'
}

/**
 * Admin request interface
 */
export interface AdminRequest {
  type: AdminRequestType;
  prompt: string;
  parameters?: Record<string, any>;
}

/**
 * Admin response interface
 */
export interface AdminResponse {
  success: boolean;
  data?: any;
  message?: string;
  error?: string;
  requestId?: string;
}

/**
 * Process a natural language admin request
 * 
 * @param {string} prompt - The natural language prompt
 * @returns {Promise<AdminResponse>} The response from the AI processor
 */
export const processNaturalLanguageRequest = async (prompt: string): Promise<AdminResponse> => {
  try {
    // Track the request
    trackEvent(
      EventCategory.NAVIGATION,
      EventAction.SUBMIT,
      'admin_natural_language_request',
      undefined,
      { prompt }
    );

    // In a real implementation, this would call an AI service
    // For now, we'll simulate the AI processing

    // Extract intent and entities from the prompt
    const { intent, entities } = extractIntentAndEntities(prompt);

    // Process based on intent
    let response: AdminResponse;

    switch (intent) {
      case 'get_offer_stats':
        response = await simulateOfferStats(entities);
        break;
      case 'update_offer':
        response = await simulateOfferUpdate(entities);
        break;
      case 'create_offer':
        response = await simulateOfferCreation(entities);
        break;
      case 'get_user_segments':
        response = await simulateUserSegments();
        break;
      default:
        response = {
          success: false,
          message: 'I could not understand your request. Please try rephrasing.',
          error: 'Unknown intent'
        };
    }

    return {
      ...response,
      requestId: generateRequestId()
    };
  } catch (error) {
    // Track error
    trackEvent(
      EventCategory.NAVIGATION,
      EventAction.ERROR,
      'admin_natural_language_error',
      undefined,
      { error: error instanceof Error ? error.message : String(error), prompt }
    );

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      requestId: generateRequestId()
    };
  }
};

/**
 * Execute a structured admin request
 * 
 * @param {AdminRequest} request - The structured admin request
 * @returns {Promise<AdminResponse>} The response
 */
export const executeAdminRequest = async (request: AdminRequest): Promise<AdminResponse> => {
  try {
    // Track the request
    trackEvent(
      EventCategory.NAVIGATION,
      EventAction.SUBMIT,
      `admin_${request.type}_request`,
      undefined,
      { request }
    );

    // In a real implementation, this would call an API
    // For now, we'll simulate the response

    let response: AdminResponse;

    switch (request.type) {
      case AdminRequestType.QUERY:
        response = await simulateQuery(request);
        break;
      case AdminRequestType.COMMAND:
        response = await simulateCommand(request);
        break;
      case AdminRequestType.REPORT:
        response = await simulateReport(request);
        break;
      case AdminRequestType.UPDATE:
        response = await simulateUpdate(request);
        break;
      case AdminRequestType.CREATE:
        response = await simulateCreate(request);
        break;
      case AdminRequestType.DELETE:
        response = await simulateDelete(request);
        break;
      default:
        response = {
          success: false,
          message: 'Invalid request type',
          error: 'Unknown request type'
        };
    }

    return {
      ...response,
      requestId: generateRequestId()
    };
  } catch (error) {
    // Track error
    trackEvent(
      EventCategory.NAVIGATION,
      EventAction.ERROR,
      `admin_${request.type}_error`,
      undefined,
      { error: error instanceof Error ? error.message : String(error), request }
    );

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      requestId: generateRequestId()
    };
  }
};

// Helper functions for simulating AI processing

/**
 * Extract intent and entities from a natural language prompt
 * 
 * @param {string} prompt - The natural language prompt
 * @returns {Object} The extracted intent and entities
 */
const extractIntentAndEntities = (prompt: string): { intent: string; entities: Record<string, any> } => {
  // In a real implementation, this would use NLP
  // For now, we'll use simple keyword matching

  const lowerPrompt = prompt.toLowerCase();

  if (lowerPrompt.includes('stats') || lowerPrompt.includes('statistics') || lowerPrompt.includes('performance')) {
    if (lowerPrompt.includes('loan') || lowerPrompt.includes('offer')) {
      return {
        intent: 'get_offer_stats',
        entities: {
          offerType: lowerPrompt.includes('personal') ? 'personal_loan' :
            lowerPrompt.includes('car') ? 'car_loan' :
              lowerPrompt.includes('debt') ? 'debt_relief' :
                lowerPrompt.includes('cash') ? 'instant_cash' : 'all'
        }
      };
    }
  }

  if (lowerPrompt.includes('update') || lowerPrompt.includes('change') || lowerPrompt.includes('modify')) {
    if (lowerPrompt.includes('loan') || lowerPrompt.includes('offer')) {
      return {
        intent: 'update_offer',
        entities: {
          offerType: lowerPrompt.includes('personal') ? 'personal_loan' :
            lowerPrompt.includes('car') ? 'car_loan' :
              lowerPrompt.includes('debt') ? 'debt_relief' :
                lowerPrompt.includes('cash') ? 'instant_cash' : 'unknown',
          field: lowerPrompt.includes('title') ? 'title' :
            lowerPrompt.includes('description') ? 'description' :
              lowerPrompt.includes('benefit') ? 'benefits' : 'unknown',
          value: extractValueFromPrompt(prompt)
        }
      };
    }
  }

  if (lowerPrompt.includes('create') || lowerPrompt.includes('add') || lowerPrompt.includes('new')) {
    if (lowerPrompt.includes('loan') || lowerPrompt.includes('offer')) {
      return {
        intent: 'create_offer',
        entities: {
          offerType: lowerPrompt.includes('personal') ? 'personal_loan' :
            lowerPrompt.includes('car') ? 'car_loan' :
              lowerPrompt.includes('debt') ? 'debt_relief' :
                lowerPrompt.includes('cash') ? 'instant_cash' : 'custom',
          title: extractTitleFromPrompt(prompt),
          description: extractDescriptionFromPrompt(prompt)
        }
      };
    }
  }

  if (lowerPrompt.includes('segment') || lowerPrompt.includes('user group') || lowerPrompt.includes('audience')) {
    return {
      intent: 'get_user_segments',
      entities: {}
    };
  }

  return {
    intent: 'unknown',
    entities: {}
  };
};

/**
 * Extract a value from a prompt
 */
const extractValueFromPrompt = (prompt: string): string => {
  // In a real implementation, this would use more sophisticated NLP
  // For now, we'll use a simple heuristic
  const words = prompt.split(' ');
  const toIndex = words.findIndex(w => w.toLowerCase() === 'to');

  if (toIndex !== -1 && toIndex < words.length - 1) {
    return words.slice(toIndex + 1).join(' ');
  }

  return '';
};

/**
 * Extract a title from a prompt
 */
const extractTitleFromPrompt = (prompt: string): string => {
  // Simple extraction for demo purposes
  const titleMatch = prompt.match(/title[:\s]+([^,\.]+)/i);
  return titleMatch ? titleMatch[1].trim() : 'New Offer';
};

/**
 * Extract a description from a prompt
 */
const extractDescriptionFromPrompt = (prompt: string): string => {
  // Simple extraction for demo purposes
  const descMatch = prompt.match(/description[:\s]+([^,\.]+)/i);
  return descMatch ? descMatch[1].trim() : 'Description for the new offer';
};

/**
 * Generate a request ID
 */
const generateRequestId = (): string => {
  return `req_${Math.random().toString(36).substring(2, 15)}_${Date.now().toString(36)}`;
};

// Simulation functions for demo purposes

/**
 * Simulate offer statistics
 */
const simulateOfferStats = async (entities: Record<string, any>): Promise<AdminResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));

  const stats = {
    personal_loan: {
      views: 1245,
      clicks: 387,
      conversions: 89,
      conversionRate: '23.0%'
    },
    debt_relief: {
      views: 876,
      clicks: 213,
      conversions: 42,
      conversionRate: '19.7%'
    },
    car_loan: {
      views: 932,
      clicks: 301,
      conversions: 67,
      conversionRate: '22.3%'
    },
    instant_cash: {
      views: 1532,
      clicks: 412,
      conversions: 103,
      conversionRate: '25.0%'
    }
  };

  return {
    success: true,
    data: entities.offerType === 'all' ? stats : stats[entities.offerType as keyof typeof stats],
    message: `Here are the statistics for ${entities.offerType === 'all' ? 'all offers' : entities.offerType}`
  };
};

/**
 * Simulate offer update
 */
const simulateOfferUpdate = async (entities: Record<string, any>): Promise<AdminResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 700));

  if (entities.offerType === 'unknown' || entities.field === 'unknown') {
    return {
      success: false,
      error: 'Could not determine which offer or field to update',
      message: 'Please specify the offer type and field to update'
    };
  }

  return {
    success: true,
    data: {
      offerType: entities.offerType,
      field: entities.field,
      value: entities.value,
      updatedAt: new Date().toISOString()
    },
    message: `Successfully updated ${entities.field} for ${entities.offerType}`
  };
};

/**
 * Simulate offer creation
 */
const simulateOfferCreation = async (entities: Record<string, any>): Promise<AdminResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));

  return {
    success: true,
    data: {
      id: `offer_${Math.random().toString(36).substring(2, 9)}`,
      offerType: entities.offerType,
      title: entities.title,
      description: entities.description,
      createdAt: new Date().toISOString()
    },
    message: `Successfully created new offer: ${entities.title}`
  };
};

/**
 * Simulate user segments
 */
const simulateUserSegments = async (): Promise<AdminResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 600));

  return {
    success: true,
    data: {
      segments: [
        {
          id: 'young_professional',
          name: 'Young Professionals',
          count: 3245,
          primaryOffer: 'personal_loan'
        },
        {
          id: 'family',
          name: 'Family Households',
          count: 2876,
          primaryOffer: 'debt_relief'
        },
        {
          id: 'business_owner',
          name: 'Business Owners',
          count: 1532,
          primaryOffer: 'car_loan'
        },
        {
          id: 'student',
          name: 'Students',
          count: 2143,
          primaryOffer: 'instant_cash'
        }
      ]
    },
    message: 'Successfully retrieved user segments'
  };
};

/**
 * Simulate query
 */
const simulateQuery = async (request: AdminRequest): Promise<AdminResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));

  return {
    success: true,
    data: {
      results: [
        { id: 1, name: 'Result 1', value: Math.random() * 100 },
        { id: 2, name: 'Result 2', value: Math.random() * 100 },
        { id: 3, name: 'Result 3', value: Math.random() * 100 }
      ],
      count: 3,
      query: request.prompt
    },
    message: `Query executed successfully: ${request.prompt}`
  };
};

/**
 * Simulate command
 */
const simulateCommand = async (request: AdminRequest): Promise<AdminResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 600));

  return {
    success: true,
    data: {
      command: request.prompt,
      executed: true,
      timestamp: new Date().toISOString()
    },
    message: `Command executed successfully: ${request.prompt}`
  };
};

/**
 * Simulate report
 */
const simulateReport = async (request: AdminRequest): Promise<AdminResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 800));

  return {
    success: true,
    data: {
      reportName: request.prompt,
      generatedAt: new Date().toISOString(),
      data: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
        datasets: [
          {
            label: 'Conversions',
            data: [65, 59, 80, 81, 56]
          },
          {
            label: 'Views',
            data: [28, 48, 40, 19, 86]
          }
        ]
      }
    },
    message: `Report generated successfully: ${request.prompt}`
  };
};

/**
 * Simulate update
 */
const simulateUpdate = async (request: AdminRequest): Promise<AdminResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 700));

  return {
    success: true,
    data: {
      updated: true,
      target: request.parameters?.target || 'unknown',
      changes: request.parameters?.changes || {},
      timestamp: new Date().toISOString()
    },
    message: `Update executed successfully: ${request.prompt}`
  };
};

/**
 * Simulate create
 */
const simulateCreate = async (request: AdminRequest): Promise<AdminResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 900));

  return {
    success: true,
    data: {
      created: true,
      id: `new_${Math.random().toString(36).substring(2, 9)}`,
      type: request.parameters?.type || 'unknown',
      properties: request.parameters?.properties || {},
      timestamp: new Date().toISOString()
    },
    message: `Creation executed successfully: ${request.prompt}`
  };
};

/**
 * Simulate delete
 */
const simulateDelete = async (request: AdminRequest): Promise<AdminResponse> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 600));

  return {
    success: true,
    data: {
      deleted: true,
      id: request.parameters?.id || 'unknown',
      type: request.parameters?.type || 'unknown',
      timestamp: new Date().toISOString()
    },
    message: `Deletion executed successfully: ${request.prompt}`
  };
}; 