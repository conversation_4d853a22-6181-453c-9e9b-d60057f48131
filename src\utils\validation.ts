/**
 * Validation Utility
 *
 * Common validation functions for forms and inputs.
 */
export const validation = {
  /**
   * Validate phone number (Iraqi format)
   *
   * @param {string} phone - The phone number to validate
   * @returns {boolean} Whether the phone number is valid
   */
  isValidPhone(phone: string): boolean {
    // Iraqi phone number format: starts with 07 followed by 8 more digits
    const phoneRegex = /^07[0-9]{8}$/;
    return phoneRegex.test(phone);
  },

  /**
   * Validate email
   *
   * @param {string} email - The email to validate
   * @returns {boolean} Whether the email is valid
   */
  isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  /**
   * Validate password strength
   *
   * @param {string} password - The password to validate
   * @returns {boolean} Whether the password is strong enough
   */
  isStrongPassword(password: string): boolean {
    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/;
    return passwordRegex.test(password);
  },

  /**
   * Validate credit card number using Luhn algorithm
   *
   * @param {string} cardNumber - The card number to validate
   * @returns {boolean} Whether the card number is valid
   */
  isValidCreditCard(cardNumber: string): boolean {
    // Remove spaces and dashes
    const sanitized = cardNumber.replace(/[\s-]/g, "");

    // Check if contains only digits and has valid length
    if (!/^\d{13,19}$/.test(sanitized)) {
      return false;
    }

    // Luhn algorithm
    let sum = 0;
    let double = false;

    // Loop from right to left
    for (let i = sanitized.length - 1; i >= 0; i--) {
      let digit = parseInt(sanitized.charAt(i), 10);

      // Double every second digit
      if (double) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }

      sum += digit;
      double = !double;
    }

    return sum % 10 === 0;
  },

  /**
   * Validate card expiry date (MM/YY format)
   *
   * @param {string} expiryDate - The expiry date to validate
   * @returns {boolean} Whether the expiry date is valid
   */
  isValidExpiryDate(expiryDate: string): boolean {
    // Check format (MM/YY)
    if (!/^\d{2}\/\d{2}$/.test(expiryDate)) {
      return false;
    }

    const [monthStr, yearStr] = expiryDate.split("/");
    const month = parseInt(monthStr, 10);
    const year = parseInt(yearStr, 10) + 2000; // Convert to 4-digit year

    // Check month is valid
    if (month < 1 || month > 12) {
      return false;
    }

    // Get current date
    const now = new Date();
    const currentMonth = now.getMonth() + 1; // getMonth() is 0-indexed
    const currentYear = now.getFullYear();

    // Check if card is expired
    if (year < currentYear || (year === currentYear && month < currentMonth)) {
      return false;
    }

    return true;
  },

  /**
   * Validate CVV (3 or 4 digits)
   *
   * @param {string} cvv - The CVV to validate
   * @returns {boolean} Whether the CVV is valid
   */
  isValidCVV(cvv: string): boolean {
    return /^\d{3,4}$/.test(cvv);
  },
};

// Named exports for direct importing
export const validatePhone = validation.isValidPhone;
export const validateEmail = validation.isValidEmail;
export const validatePassword = validation.isStrongPassword;
export const validateCreditCard = validation.isValidCreditCard;
export const validateExpiryDate = validation.isValidExpiryDate;
export const validateCVV = validation.isValidCVV;

export default validation;
