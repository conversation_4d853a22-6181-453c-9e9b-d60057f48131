import React, {
  create<PERSON>ontext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useState,
} from "react";
import { IntlProvider } from "react-intl";
import { useLocation, useNavigate } from "react-router-dom";

// Language type with only Arabic and Kurdish
type Language = "ar" | "ku";

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
  isLoading: boolean;
  resetPageView: () => void;
  // All languages are RTL, so we don't need direction property
}

const LanguageContext = createContext<LanguageContextType | undefined>(
  undefined
);

interface LanguageProviderProps {
  children: ReactNode;
}

/**
 * Manages language preferences and dynamic translations in the application.
 * @example
 * LanguageContextProviderComponent({children})
 * Provides context with language settings and translation functions wrapped in IntlProvider.
 * @param {Object} props 
 * @param {ReactNode} props.children - The children components that will receive the language context.
 * @returns {JSX.Element} A context provider wrapping IntlProvider component for managing language and translations.
 * @description
 *   - Supports Arabic and Kurdish languages with RTL directionality.
 *   - Integrates with `useLocation` and `useNavigate` for language-based routing.
 *   - Automatically updates language preference in local storage.
 *   - Includes a loading state for smooth language transition animations.
 */
export const LanguageProvider: React.FC<LanguageProviderProps> = ({
  children,
}) => {
  const location = useLocation();
  const navigate = useNavigate();

  // Get language from URL path or localStorage or default to Arabic
  const [language, setLanguage] = useState<Language>(() => {
    // First check if the URL already has a language prefix
    const pathParts = location.pathname.split("/").filter(Boolean);
    const urlLang = pathParts[0];

    if (urlLang === "ar" || urlLang === "ku") {
      return urlLang;
    }

    // If no valid language in URL, check localStorage
    const savedLanguage = localStorage.getItem("language");
    return (savedLanguage as Language) || "ar";
  });

  // Add loading state for transitions
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Load translations
  const [translations, setTranslations] = useState<
    Record<string, Record<string, string>>
  >({
    ar: {},
    ku: {},
  });

  // Function to reset page view - scrolls to top and resets any UI state
  const resetPageView = useCallback(() => {
    // Scroll to top of the page
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });

    // Add any additional reset logic here if needed
  }, []);

  // Use effect to update URL when language changes
  useEffect(() => {
    const pathParts = location.pathname.split("/").filter(Boolean);
    const urlLang = pathParts[0];

    // Function to check if language is valid
    const isValidLanguage = (lang: string): lang is Language => {
      return lang === "ar" || lang === "ku";
    };

    // If URL already has a language prefix that matches current language, do nothing
    if (isValidLanguage(urlLang) && urlLang === language) {
      return;
    }

    // If URL has a language prefix but it's different from current language
    if (isValidLanguage(urlLang) && urlLang !== language) {
      // Replace the language prefix
      const newPath =
        "/" + language + location.pathname.substring(("/" + urlLang).length);
      navigate(newPath, { replace: true });
      return;
    }

    // If URL doesn't have a language prefix, add it
    if (!isValidLanguage(urlLang)) {
      navigate(
        `/${language}${location.pathname === "/" ? "" : location.pathname}`,
        { replace: true }
      );
    }
  }, [language, location.pathname, navigate]);

  // Enhanced language change function with loading state
  const handleSetLanguage = useCallback(
    (lang: Language) => {
      // Don't do anything if it's the same language
      if (lang === language) return;

      // Start loading transition
      setIsLoading(true);

      // Set language after a short delay to allow for transition animation
      setTimeout(() => {
        setLanguage(lang);
        // End loading will be handled in the useEffect
      }, 300);
    },
    [language]
  );

  useEffect(() => {
    // Save language preference to localStorage
    localStorage.setItem("language", language);

    // Update document direction and language
    document.documentElement.dir = "rtl"; // Both Arabic and Kurdish are RTL
    document.documentElement.lang = language;

    // Load translations dynamically
    /**
    * Loads and applies language translations asynchronously.
    * @example
    * sync();
    * // Loads translations and updates the state accordingly.
    * @returns {void} Ensures translations are set and updates the loading state.
    * @description
    *   - Imports Arabic and Kurdish translations dynamically.
    *   - Sets the translations state for Arabic and Kurdish languages.
    *   - Ends the loading state and resets the page view once translations are loaded.
    *   - Handles errors by logging them and ensuring the loading state is reset.
    */
    const loadTranslations = async () => {
      try {
        const arTranslations = await import("../locales/ar.json");
        const kuTranslations = await import("../locales/ku.json");

        setTranslations({
          ar: arTranslations.default,
          ku: kuTranslations.default,
        });

        // End loading state after translations are loaded
        setTimeout(() => {
          setIsLoading(false);
          // Reset page view after language change is complete
          resetPageView();
        }, 500);
      } catch (error) {
        console.error("Failed to load translations:", error);
        setIsLoading(false); // Ensure loading state is reset even on error
      }
    };

    loadTranslations();
  }, [language, resetPageView]);

  // Translation function
  const t = (key: string): string => {
    const currentTranslations = translations[language] || {};
    return currentTranslations[key] || key;
  };

  // Create the context value
  const contextValue = {
    language,
    setLanguage: handleSetLanguage,
    t,
    isLoading,
    resetPageView,
  };

  // Messages for IntlProvider, derived from translations
  const messages = translations[language] || {};

  return (
    <LanguageContext.Provider value={contextValue}>
      <IntlProvider
        locale={language}
        messages={messages}
        defaultLocale="ar"
        onError={(err) => {
          // Only log errors that aren't related to missing data
          if (!err.message.includes("MISSING_DATA")) {
            console.error("Intl Error:", err);
          }
        }}
      >
        {children}
      </IntlProvider>
    </LanguageContext.Provider>
  );
};

// Custom hook to use the language context
export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider");
  }
  return context;
};
