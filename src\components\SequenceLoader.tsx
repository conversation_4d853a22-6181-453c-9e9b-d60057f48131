import { AnimatePresence, motion } from "framer-motion";
import { CreditC<PERSON>, Lock, ShieldCheck } from "lucide-react";
import React, { useEffect, useState } from "react";

interface SequenceLoaderProps {
  isActive: boolean;
  onComplete: () => void;
}

/**
 * Displays a sequence of security steps with corresponding animations and progress to the user.
 * @example
 * SequenceLoader({ isActive: true, onComplete: () => console.log('Completed') });
 * 
 * @param {boolean} isActive - Whether the sequence loader is active and should display.
 * @param {function} onComplete - Callback to execute once all steps are completed.
 * @returns {JSX.Element} A React component displaying the sequence loader with animations.
 * @description
 *   - Locks document scrolling while the sequence is active.
 *   - Consists of three animated steps: securing connection, data verification, and form preparation.
 *   - Uses `requestAnimationFrame` for smooth progress updates.
 *   - Restores document scroll and invokes `onComplete` callback after sequence finishes.
 */
const SequenceLoader: React.FC<SequenceLoaderProps> = ({
  isActive,
  onComplete,
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState(0);

  const securitySteps = [
    {
      icon: <Lock className="w-8 h-8 text-white" />,
      text: "تأمين الاتصال",
      description: "جاري إنشاء اتصال آمن",
      duration: 1500,
      color: "bg-blue-600",
    },
    {
      icon: <ShieldCheck className="w-8 h-8 text-white" />,
      text: "تحقق من البيانات",
      description: "جاري التحقق من صحة البيانات",
      duration: 1500,
      color: "bg-primary",
    },
    {
      icon: <CreditCard className="w-8 h-8 text-white" />,
      text: "تجهيز النموذج",
      description: "جاري تجهيز نموذج البطاقة",
      duration: 1500,
      color: "bg-primary",
    },
  ];

  useEffect(() => {
    if (!isActive) {
      setCurrentStep(0);
      setProgress(0);
      return;
    }

    let animationFrameId: number;
    let timeoutId: NodeJS.Timeout;

    /**
     * Executes a sequence of security steps with progress animation.
     * @example
     * sequenceLoader()
     * // Animation and processing of security steps starts, and upon completion, the body overflow style is reset and onComplete is called.
     * @async
     * @returns {Promise<void>} Resolves after all security steps have been processed and onComplete is called.
     * @description
     *   - Disables page scroll during the execution by setting document body overflow to "hidden".
     *   - Each security step updates a progress value from 0 to 100 over a specified duration.
     *   - After each step's progress reaches 100%, a brief delay ensures complete processing before proceeding.
     *   - Restores the document body overflow style and triggers a completion callback when all steps are done.
     */
    const runSequence = async () => {
      document.body.style.overflow = "hidden";

      for (let i = 0; i < securitySteps.length; i++) {
        setCurrentStep(i);
        setProgress(0);

        const step = securitySteps[i];
        const startTime = Date.now();

        await new Promise<void>((resolve) => {
          /**
          * Updates the progress of an animation based on elapsed time
          * @example
          * updateProgress()
          * // Progress updated to a new value or resolves after completion
          * @param {number} startTime - The initial timestamp when the animation starts.
          * @param {object} step - An object containing the duration of the animation step.
          * @returns {void} This function does not return a value.
          * @description
          *   - Uses requestAnimationFrame to repeatedly update the progress until it reaches 100%.
          *   - Sets a timeout to resolve once the progress reaches 100%.
          */
          const updateProgress = () => {
            const elapsed = Date.now() - startTime;
            const newProgress = Math.min(100, (elapsed / step.duration) * 100);

            setProgress(newProgress);

            if (newProgress < 100) {
              animationFrameId = requestAnimationFrame(updateProgress);
            } else {
              timeoutId = setTimeout(resolve, 300);
            }
          };

          animationFrameId = requestAnimationFrame(updateProgress);
        });
      }

      // All steps completed
      setTimeout(() => {
        document.body.style.overflow = "";
        onComplete();
      }, 500);
    };

    runSequence();

    return () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      document.body.style.overflow = "";
    };
  }, [isActive, onComplete]);

  return (
    <AnimatePresence>
      {isActive && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.4 }}
          className="fixed inset-0 bg-black/80 backdrop-blur-md z-50 flex items-center justify-center"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{
              duration: 0.4,
              type: "spring",
              stiffness: 350,
              damping: 25,
            }}
            className="bg-white rounded-2xl p-6 max-w-md w-full mx-4 overflow-hidden relative shadow-2xl"
          >
            {/* Background decoration */}
            <motion.div
              className="absolute -top-20 -right-20 w-40 h-40 rounded-full bg-primary/5"
              animate={{ rotate: 360 }}
              transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
            />
            <motion.div
              className="absolute -bottom-20 -left-20 w-40 h-40 rounded-full bg-primary/5"
              animate={{ rotate: -360 }}
              transition={{ duration: 25, repeat: Infinity, ease: "linear" }}
            />

            <div className="relative">
              <div className="text-center mb-8">
                <motion.div
                  initial={{ scale: 0.8 }}
                  animate={{ scale: [0.95, 1.05, 0.95] }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                  className={`w-20 h-20 rounded-full ${securitySteps[currentStep].color} flex items-center justify-center mx-auto mb-6`}
                >
                  {securitySteps[currentStep].icon}
                </motion.div>

                <motion.h3 className="text-xl font-bold mb-2">
                  {securitySteps[currentStep].text}
                </motion.h3>

                <motion.p
                  className="text-gray-600 text-sm"
                  initial={{ opacity: 0.7 }}
                  animate={{ opacity: 1 }}
                >
                  {securitySteps[currentStep].description}
                </motion.p>
              </div>

              {/* Steps indicators */}
              <div className="flex justify-center space-x-2 rtl:space-x-reverse mb-6">
                {securitySteps.map((_, index) => (
                  <motion.div
                    key={index}
                    className={`w-3 h-3 rounded-full ${
                      index === currentStep
                        ? securitySteps[currentStep].color
                        : index < currentStep
                          ? "bg-green-500"
                          : "bg-gray-300"
                    }`}
                    animate={
                      index === currentStep
                        ? {
                            scale: [1, 1.2, 1],
                            opacity: [0.7, 1, 0.7],
                          }
                        : {}
                    }
                    transition={{
                      duration: 1,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  />
                ))}
              </div>

              {/* Enhanced Progress Bar */}
              <div className="relative h-3 bg-gray-100 rounded-full overflow-hidden">
                {/* Background pulse effect */}
                <motion.div
                  className="absolute inset-0 bg-primary/10"
                  animate={{
                    opacity: [0.3, 0.6, 0.3],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />

                {/* Actual progress bar */}
                <motion.div
                  className={`h-full ${securitySteps[currentStep].color}`}
                  initial={{ width: 0 }}
                  animate={{ width: `${progress}%` }}
                  transition={{ ease: "easeOut" }}
                />

                {/* Animated highlight effect */}
                <motion.div
                  className="absolute top-0 bottom-0 w-20 bg-gradient-to-r from-white/0 via-white/30 to-white/0"
                  animate={{
                    left: ["-20%", "120%"],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />
              </div>

              <motion.div
                className="mt-6 text-center text-xs text-gray-500"
                animate={{ opacity: [0.6, 1, 0.6] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                يرجى الانتظار، جاري تجهيز الصفحة
              </motion.div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default SequenceLoader;
