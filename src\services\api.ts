import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// API configuration
const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_URL || '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
};

/**
 * API Client
 * 
 * A wrapper around axios for making API requests.
 * Includes interceptors for request and response handling.
 */
class ApiClient {
  private client: AxiosInstance;

  constructor(config: AxiosRequestConfig = {}) {
    this.client = axios.create({
      ...API_CONFIG,
      ...config,
    });

    this.setupInterceptors();
  }

  /**
   * Setup request and response interceptors
   */
  private setupInterceptors(): void {
    // Request interceptor
    this.client.interceptors.request.use(
      (config) => {
        // Get token from localStorage
        const token = localStorage.getItem('auth_token');

        // Add token to headers if it exists
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        // Handle specific error cases
        if (error.response) {
          // Handle 401 Unauthorized
          if (error.response.status === 401) {
            // Clear auth data and redirect to login
            localStorage.removeItem('auth_token');
            window.location.href = '/';
          }

          // Handle 403 Forbidden
          if (error.response.status === 403) {
            console.error('Permission denied');
          }

          // Handle 500 Server Error
          if (error.response.status >= 500) {
            console.error('Server error');
          }
        } else if (error.request) {
          // Network error
          console.error('Network error');
        } else {
          // Other errors
          console.error('Error', error.message);
        }

        return Promise.reject(error);
      }
    );
  }

  /**
   * Make a GET request
   * 
   * @param {string} url - The URL to request
   * @param {AxiosRequestConfig} config - Additional config
   * @returns {Promise<AxiosResponse>} The response
   */
  public get<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.get<T>(url, config);
  }

  /**
   * Make a POST request
   * 
   * @param {string} url - The URL to request
   * @param {any} data - The data to send
   * @param {AxiosRequestConfig} config - Additional config
   * @returns {Promise<AxiosResponse>} The response
   */
  public post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.post<T>(url, data, config);
  }

  /**
   * Make a PUT request
   * 
   * @param {string} url - The URL to request
   * @param {any} data - The data to send
   * @param {AxiosRequestConfig} config - Additional config
   * @returns {Promise<AxiosResponse>} The response
   */
  public put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.put<T>(url, data, config);
  }

  /**
   * Make a DELETE request
   * 
   * @param {string} url - The URL to request
   * @param {AxiosRequestConfig} config - Additional config
   * @returns {Promise<AxiosResponse>} The response
   */
  public delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.delete<T>(url, config);
  }
}

// Create and export a singleton instance
export const api = new ApiClient();

export default api; 