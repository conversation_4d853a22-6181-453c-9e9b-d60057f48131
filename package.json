{"name": "fib-banking", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:dev": "tsc && vite build --mode development", "preview": "vite preview", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "format": "prettier --write \"src/**/*.{ts,tsx}\"", "analyze": "vite-bundle-visualizer", "deploy": "npm run build && surge dist fibeqtexiting.surge.sh", "deploy:dev": "npm run build:dev && surge dist fibeqtexiting.surge.sh"}, "dependencies": {"@chakra-ui/react": "^3.13.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@formatjs/intl-locale": "^4.2.10", "@formatjs/intl-numberformat": "^8.15.3", "@types/express": "^5.0.0", "@types/socket.io-client": "^1.4.36", "axios": "^1.8.3", "canvas-confetti": "^1.9.3", "clsx": "^2.1.0", "date-fns": "^3.6.0", "express": "^4.21.2", "framer-motion": "^11.18.2", "lucide-react": "^0.344.0", "react": "^18.2.0", "react-credit-cards-2": "^1.0.3", "react-dom": "^18.2.0", "react-intl": "^7.1.6", "react-otp-input": "^3.1.1", "react-router-dom": "^6.22.3", "socket.io-client": "^4.8.1", "tailwind-merge": "^2.2.1", "uuid": "^9.0.1", "zustand": "^5.0.3"}, "devDependencies": {"@types/canvas-confetti": "^1.9.0", "@types/node": "^20.11.25", "@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.31", "prettier": "^3.2.5", "tailwindcss": "^3.3.5", "terser": "^5.28.1", "typescript": "^5.2.2", "vite": "^5.1.4", "vite-bundle-visualizer": "^1.0.1", "vite-plugin-pwa": "^0.19.2"}}