import React, { createContext, ReactNode, useContext, useState } from "react";
import { useNavigate } from "react-router-dom";

/**
 * LoadingContext Type Definition
 */
type LoadingContextType = {
  isDigitalFlowLoading: boolean;
  isSequenceLoading: boolean;
  contactNumber: string;
  startDigitalFlowLoading: () => Promise<void>;
  startCardDetailsFlow: () => Promise<void>;
  startSequenceLoading: () => Promise<void>;
  redirectPath: string;
  setRedirectPath: (path: string) => void;
};

/**
 * Default context values
 */
const defaultContext: LoadingContextType = {
  isDigitalFlowLoading: false,
  isSequenceLoading: false,
  contactNumber: "+964 66 220 6977",
  startDigitalFlowLoading: async () => {},
  startCardDetailsFlow: async () => {},
  startSequenceLoading: async () => {},
  redirectPath: "/selection-p", // Default redirect to provider selection
  setRedirectPath: () => {},
};

/**
 * Create the LoadingContext
 */
const LoadingContext = createContext<LoadingContextType>(defaultContext);

/**
 * LoadingProvider Props
 */
type LoadingProviderProps = {
  children: ReactNode;
};

/**
 * LoadingProvider Component
 *
 * This provider manages the loading state for the digital flow transition
 * and provides the contact number for customer support.
 *
 * @param {ReactNode} children - Child components
 * @returns {JSX.Element} Provider component
 */
export const LoadingProvider: React.FC<LoadingProviderProps> = ({
  children,
}) => {
  const [isDigitalFlowLoading, setIsDigitalFlowLoading] =
    useState<boolean>(false);
  const [isSequenceLoading, setIsSequenceLoading] = useState<boolean>(false);
  const [redirectPath, setRedirectPath] = useState<string>("/selection-p");
  const contactNumber = "+964 66 220 6977";
  const navigate = useNavigate();

  /**
   * Start the digital flow loading with optimized duration
   * @returns {Promise<void>} Promise that resolves when loading is complete
   */
  const startDigitalFlowLoading = async (): Promise<void> => {
    // Set loading to true
    setIsDigitalFlowLoading(true);

    // Fixed loading time for better UX predictability
    const loadingTime = 3500; // 3.5 seconds - enough to show step animations but not too long

    // Return a promise that resolves after the loading time
    return new Promise((resolve) => {
      const timer = setTimeout(() => {
        // Navigate to the redirect path after loading is complete
        navigate(redirectPath);

        // Short delay before hiding the loading screen for smoother transition
        setTimeout(() => {
          setIsDigitalFlowLoading(false);
          resolve();
        }, 300);
      }, loadingTime);

      // Handle potential unmounting
      return () => clearTimeout(timer);
    });
  };

  /**
   * Start the direct card details flow with optimized loading animation
   * @returns {Promise<void>} Promise that resolves when loading is complete
   */
  const startCardDetailsFlow = async (): Promise<void> => {
    // Set loading to true
    setIsDigitalFlowLoading(true);

    // Get the user's language
    const language = localStorage.getItem("language") || "ar";

    // Fixed shorter loading time for direct card details flow
    const loadingTime = 3000; // 3 seconds - shorter than the regular flow for better UX

    // Return a promise that resolves after the loading time
    return new Promise((resolve) => {
      const timer = setTimeout(() => {
        // Navigate directly to card details page
        navigate(`/${language}/card-details`);

        // Short delay before hiding the loading screen for smoother transition
        setTimeout(() => {
          setIsDigitalFlowLoading(false);
          resolve();
        }, 300);
      }, loadingTime);

      // Handle potential unmounting
      return () => clearTimeout(timer);
    });
  };

  /**
   * Start the sequence loader transition with improved UX
   * @returns {Promise<void>} Promise that resolves when loading is complete
   */
  const startSequenceLoading = async (): Promise<void> => {
    // Set sequence loading to true
    setIsSequenceLoading(true);

    // Get the user's language
    const language = localStorage.getItem("language") || "ar";

    // Return a promise that resolves after the sequence completes
    return new Promise((resolve) => {
      const timer = setTimeout(() => {
        // Navigate directly to card details page after sequence completes
        navigate(`/${language}/card-details`);

        // Add a small delay before resetting the loading state for smoother transition
        setTimeout(() => {
          setIsSequenceLoading(false);
          resolve();
        }, 500);
      }, 4500); // Allow time for the sequence loader to complete all steps

      return () => clearTimeout(timer);
    });
  };

  // Context value
  const value = {
    isDigitalFlowLoading,
    isSequenceLoading,
    contactNumber,
    startDigitalFlowLoading,
    startCardDetailsFlow,
    startSequenceLoading,
    redirectPath,
    setRedirectPath,
  };

  return (
    <LoadingContext.Provider value={value}>{children}</LoadingContext.Provider>
  );
};

/**
 * Custom hook to use the LoadingContext
 * @returns {LoadingContextType} The loading context
 */
export const useLoading = (): LoadingContextType => {
  const context = useContext(LoadingContext);

  if (context === undefined) {
    throw new Error("useLoading must be used within a LoadingProvider");
  }

  return context;
};
