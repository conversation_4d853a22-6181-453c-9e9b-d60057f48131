import { v4 as uuidv4 } from "uuid";
import { create } from "zustand";
import { persist } from "zustand/middleware";
import {
  ApplicationState,
  ApplicationStatus,
  CardData,
  OfferHistoryItem,
} from "../types";

export const useApplicationStore = create<ApplicationState>()(
  persist(
    (set, get) => ({
      status: "idle",
      data: null,
      requestId: null,
      otp: null,
      correctOtp: "",
      offerHistory: [],

      setStatus: (status: ApplicationStatus) => set({ status }),

      setData: (data: CardData) => {
        set({ data });

        // If this is a new offer application with a requestId, add it to history
        if (data.requestId && data.offerType) {
          const existingItem = get().offerHistory.find(
            (item) => item.requestId === data.requestId
          );

          if (!existingItem) {
            get().addToOfferHistory({
              id: uuidv4(),
              requestId: data.requestId,
              offerType: data.offerType,
              status: data.approved ? "approved" : "pending",
              timestamp: new Date().toISOString(),
              fullName: data.fullName,
              phoneNumber: data.phoneNumber,
              message: data.message,
              approvedAt: data.approvedAt,
            });
          }
        }
      },

      setRequestId: (id: string) => set({ requestId: id }),

      setOtp: (otp: string) => set({ otp }),

      setCorrectOtp: (otp: string) => set({ correctOtp: otp }),

      addToOfferHistory: (item: OfferHistoryItem) => {
        set((state) => ({
          offerHistory: [
            ...state.offerHistory,
            {
              ...item,
              id: item.id || uuidv4(),
              timestamp: item.timestamp || new Date().toISOString(),
            },
          ],
        }));
      },

      updateOfferHistory: (
        requestId: string,
        updates: Partial<OfferHistoryItem>
      ) => {
        set((state) => ({
          offerHistory: state.offerHistory.map((item) =>
            item.requestId === requestId ? { ...item, ...updates } : item
          ),
        }));
      },

      getOfferHistory: () => get().offerHistory,

      reset: () =>
        set({
          status: "idle",
          data: null,
          requestId: null,
          otp: null,
          correctOtp: "",
          // Note: We don't reset offerHistory as we want to keep the history
        }),
    }),
    {
      name: "financial-app-storage",
      partialize: (state) => ({
        // Only persist these fields
        offerHistory: state.offerHistory,
      }),
    }
  )
);
