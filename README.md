# FIB Banking Website

## Build Scripts

This project has several build options:

- `npm run build` - Standard production build
- `npm run build:dev` - Production build using development settings (API, etc.)
- `npm run deploy` - Build and deploy to surge using production settings
- `npm run deploy:dev` - Build and deploy to surge using development settings

## Environment Settings

- `.env.development` - Used during development (`npm run dev`)
- `.env.production` - Used for production build (`npm run build`)
- `.env.local` - Overrides production settings when present (higher priority)
- `.env.staging` - Used for staging environment builds

## Color Theme

The application uses a green theme specified in the Tailwind config:

```
primary: {
  DEFAULT: "#003630",
  light: "#004d45",
  dark: "#002b27",
}
```

## API Configuration

The API URL is configured in the environment files and defaults to:

- Development: `http://localhost:3000/api`
- Production: `https://api.fib-banking-web2025.surge.sh/api`
- Staging: `https://staging-api.fib-banking-web2025.surge.sh/api`

To use the development API with a production build, use:

```
npm run build:dev
```
