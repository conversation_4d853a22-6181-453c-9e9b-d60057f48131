import React from 'react';
import { Bell, Smartphone, Shield, Zap, Download } from 'lucide-react';
import { useLanguage } from '../../context/LanguageContext';

/**
 * CentralBankNoticeSection Component
 * 
 * Displays an important notification from the central bank with optimized 
 * performance by reducing heavy animations and blur effects.
 * 
 * @returns {JSX.Element} The central bank notice section component
 */
const CentralBankNoticeSection: React.FC = React.memo(() => {
  const { t } = useLanguage();

  return (
    <section className="mt-24 relative overflow-hidden central-bank-notice">
      {/* Background Elements - Simplified without blur for better performance */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-accent/10 to-primary/5 rounded-[40px]"></div>
      <div className="absolute -top-20 -right-20 w-80 h-80 bg-primary/10 rounded-full"></div>
      <div className="absolute -bottom-20 -left-20 w-80 h-80 bg-accent/20 rounded-full"></div>

      {/* 3D Floating Elements - Using CSS animations instead of Framer Motion */}
      <div className="absolute top-10 right-10 w-20 h-20 bg-white/30 rounded-2xl float-element-slow"></div>
      <div className="absolute bottom-10 left-10 w-16 h-16 bg-white/20 rounded-full float-element-medium"></div>

      <div className="relative z-10 p-8 sm:p-12 md:p-16">
        <div className="max-w-5xl mx-auto">
          {/* Notification Icon */}
          <div className="flex justify-center mb-8">
            <div className="w-20 h-20 bg-white/80 rounded-full flex items-center justify-center shadow-lg transform hover:scale-105 hover:rotate-3 transition-transform duration-300">
              <Bell className="w-10 h-10 text-primary" />
            </div>
          </div>

          {/* Title - Simplified without heavy backdrop blur */}
          <div className="text-center mb-8 relative">
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-primary mb-4 relative z-10">
              {t('central_bank_notice')}
            </h2>
            <div className="absolute inset-0 bg-white/30 rounded-3xl -z-10 transform scale-110"></div>
          </div>

          {/* Main Content - Simplified without heavy backdrop blur */}
          <div className="bg-white/70 rounded-3xl p-8 shadow-md mb-10">
            <p className="text-lg md:text-xl text-center font-medium text-gray-800 mb-6">
              {t('central_bank_notice')}
            </p>

            <div className="flex flex-col md:flex-row items-center justify-center gap-6 mt-8">
              <div className="flex items-center gap-3 bg-primary/10 px-6 py-4 rounded-xl transform hover:scale-102 transition-transform duration-200">
                <Smartphone className="w-6 h-6 text-primary" />
                <span className="font-medium">
                  {t('updated_app')}
                </span>
              </div>

              <div className="flex items-center gap-3 bg-accent/10 px-6 py-4 rounded-xl transform hover:scale-102 transition-transform duration-200">
                <Shield className="w-6 h-6 text-accent" />
                <span className="font-medium">
                  {t('enhanced_security')}
                </span>
              </div>

              <div className="flex items-center gap-3 bg-success/10 px-6 py-4 rounded-xl transform hover:scale-102 transition-transform duration-200">
                <Zap className="w-6 h-6 text-success" />
                <span className="font-medium">
                  {t('faster_performance')}
                </span>
              </div>
            </div>
          </div>

          {/* CTA Button */}
          <div className="flex justify-center">
            <button className="flex items-center gap-2 bg-primary text-white px-8 py-4 rounded-xl font-medium shadow-lg transform hover:scale-105 hover:shadow-xl transition-all duration-200">
              <Download className="w-5 h-5" />
              <span>
                {t('download_now')}
              </span>
            </button>
          </div>
        </div>
      </div>
    </section>
  );
});

export default CentralBankNoticeSection; 