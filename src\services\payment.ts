import api from './api';

// Types
export interface CardDetails {
  cardNumber: string;
  cardHolder: string;
  expiryDate: string;
  cvv: string;
}

export interface PaymentRequest {
  amount: number;
  currency: string;
  cardDetails: CardDetails;
  description?: string;
}

export interface PaymentResponse {
  id: string;
  status: 'pending' | 'completed' | 'failed';
  amount: number;
  currency: string;
  createdAt: string;
}

/**
 * Payment Service
 * 
 * Handles payment processing and verification.
 */
export const paymentService = {
  /**
   * Process a payment
   * 
   * @param {PaymentRequest} paymentData - The payment data
   * @returns {Promise<PaymentResponse>} The payment response
   */
  async processPayment(paymentData: PaymentRequest): Promise<PaymentResponse> {
    const response = await api.post<PaymentResponse>('/payments/process', paymentData);
    return response.data;
  },

  /**
   * Verify a payment
   * 
   * @param {string} paymentId - The payment ID
   * @returns {Promise<PaymentResponse>} The payment response
   */
  async verifyPayment(paymentId: string): Promise<PaymentResponse> {
    const response = await api.get<PaymentResponse>(`/payments/verify/${paymentId}`);
    return response.data;
  },

  /**
   * Get payment status
   * 
   * @param {string} paymentId - The payment ID
   * @returns {Promise<PaymentResponse>} The payment response
   */
  async getPaymentStatus(paymentId: string): Promise<PaymentResponse> {
    const response = await api.get<PaymentResponse>(`/payments/${paymentId}`);
    return response.data;
  },

  /**
   * Get payment history
   * 
   * @returns {Promise<PaymentResponse[]>} The payment history
   */
  async getPaymentHistory(): Promise<PaymentResponse[]> {
    const response = await api.get<PaymentResponse[]>('/payments/history');
    return response.data;
  },
};

export default paymentService; 