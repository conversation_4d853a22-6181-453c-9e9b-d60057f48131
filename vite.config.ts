import react from "@vitejs/plugin-react";
import path from "path";
import { defineConfig } from "vite";
// import { VitePWA } from "vite-plugin-pwa";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    // PWA plugin temporarily disabled due to build issues
    // VitePWA({
    //   registerType: "autoUpdate",
    //   includeAssets: ["favicon.png", "banner.png"],
    //   manifest: {
    //     name: "FIB Banking",
    //     short_name: "FIB",
    //     description: "First Iraqi Bank Digital Banking",
    //     theme_color: "#003630",
    //     icons: [
    //       {
    //         src: "favicon.png",
    //         sizes: "192x192",
    //         type: "image/png",
    //       },
    //       {
    //         src: "banner.png",
    //         sizes: "512x512",
    //         type: "image/png",
    //       },
    //     ],
    //   },
    //   strategies: "injectManifest",
    //   injectManifest: {
    //     injectionPoint: undefined,
    //   },
    //   devOptions: {
    //     enabled: false,
    //     type: "module",
    //   },
    // }),
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    target: "esnext",
    outDir: "dist",
    assetsDir: "",
    sourcemap: false,
    minify: "terser",
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ["react", "react-dom", "react-router-dom", "framer-motion"],
          ui: ["lucide-react", "tailwindcss"],
        },
        chunkFileNames: "js/[name]-[hash].js",
        entryFileNames: "js/[name]-[hash].js",
        assetFileNames: "[name]-[hash][extname]",
      },
    },
  },
  optimizeDeps: {
    include: [
      "react",
      "react-dom",
      "react-router-dom",
      "framer-motion",
      "lucide-react",
    ],
  },
  server: {
    port: 3000,
    strictPort: true,
    host: true,
  },
});
